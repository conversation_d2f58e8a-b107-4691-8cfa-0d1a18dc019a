import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  FlatList,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { Colors } from '@/constants/Colors';
import { Typography, Spacing, BorderRadius } from '@/constants/Theme';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Card, Button } from '@/components/ui';
import { BookingFlow, ServiceProvider } from '@/types';

interface ServiceProviderStepProps {
  onComplete: (data: Partial<BookingFlow>) => void;
  initialData: BookingFlow;
}

export default function ServiceProviderStep({ onComplete, initialData }: ServiceProviderStepProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  
  const [selectedProvider, setSelectedProvider] = useState<ServiceProvider | null>(
    initialData.serviceProvider || null
  );
  const [providers, setProviders] = useState<ServiceProvider[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadProviders();
  }, []);

  const loadProviders = async () => {
    setIsLoading(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mock providers data
    const mockProviders: ServiceProvider[] = [
      {
        id: '1',
        businessName: 'QuickTire Mobile',
        contactPerson: 'John Smith',
        email: '<EMAIL>',
        phone: '+44 7700 900123',
        address: '123 High Street, London',
        coordinates: { latitude: 51.5074, longitude: -0.1278 },
        rating: 4.8,
        reviewCount: 156,
        isVerified: true,
        services: ['tire_change', 'tire_repair'],
        workingHours: {
          monday: { isOpen: true, openTime: '08:00', closeTime: '18:00' },
          tuesday: { isOpen: true, openTime: '08:00', closeTime: '18:00' },
          wednesday: { isOpen: true, openTime: '08:00', closeTime: '18:00' },
          thursday: { isOpen: true, openTime: '08:00', closeTime: '18:00' },
          friday: { isOpen: true, openTime: '08:00', closeTime: '18:00' },
          saturday: { isOpen: true, openTime: '09:00', closeTime: '17:00' },
          sunday: { isOpen: false },
        },
        priceRange: 'mid-range',
        distance: 2.5,
      },
      {
        id: '2',
        businessName: 'Premium Tire Services',
        contactPerson: 'Sarah Johnson',
        email: '<EMAIL>',
        phone: '+44 7700 900456',
        address: '456 Main Road, London',
        coordinates: { latitude: 51.5074, longitude: -0.1278 },
        rating: 4.9,
        reviewCount: 203,
        isVerified: true,
        services: ['tire_change', 'tire_repair', 'tire_installation'],
        workingHours: {
          monday: { isOpen: true, openTime: '07:00', closeTime: '19:00' },
          tuesday: { isOpen: true, openTime: '07:00', closeTime: '19:00' },
          wednesday: { isOpen: true, openTime: '07:00', closeTime: '19:00' },
          thursday: { isOpen: true, openTime: '07:00', closeTime: '19:00' },
          friday: { isOpen: true, openTime: '07:00', closeTime: '19:00' },
          saturday: { isOpen: true, openTime: '08:00', closeTime: '18:00' },
          sunday: { isOpen: true, openTime: '10:00', closeTime: '16:00' },
        },
        priceRange: 'premium',
        distance: 3.2,
      },
    ];
    
    setProviders(mockProviders);
    setIsLoading(false);
  };

  const handleProviderSelect = (provider: ServiceProvider) => {
    setSelectedProvider(provider);
  };

  const handleContinue = () => {
    if (selectedProvider) {
      onComplete({ serviceProvider: selectedProvider });
    }
  };

  const renderProviderCard = ({ item: provider }: { item: ServiceProvider }) => {
    const isSelected = selectedProvider?.id === provider.id;
    
    return (
      <Card
        onPress={() => handleProviderSelect(provider)}
        variant={isSelected ? 'elevated' : 'outlined'}
        padding="lg"
        style={[
          styles.providerCard,
          isSelected && {
            borderColor: colors.primary,
            borderWidth: 2,
          },
        ]}
      >
        <View style={styles.providerHeader}>
          <View style={styles.providerInfo}>
            <Text style={[
              styles.providerName,
              { color: isSelected ? colors.primary : colors.text }
            ]}>
              {provider.businessName}
            </Text>
            <Text style={[styles.providerContact, { color: colors.textSecondary }]}>
              {provider.contactPerson}
            </Text>
          </View>
          
          {isSelected && (
            <Ionicons name="checkmark-circle" size={24} color={colors.primary} />
          )}
        </View>

        <View style={styles.providerDetails}>
          <View style={styles.detailRow}>
            <Ionicons name="star" size={16} color={colors.warning} />
            <Text style={[styles.detailText, { color: colors.textSecondary }]}>
              {provider.rating.toFixed(1)} ({provider.reviewCount} reviews)
            </Text>
          </View>
          
          <View style={styles.detailRow}>
            <Ionicons name="location-outline" size={16} color={colors.textSecondary} />
            <Text style={[styles.detailText, { color: colors.textSecondary }]}>
              {provider.distance?.toFixed(1)} km away
            </Text>
          </View>
          
          <View style={styles.detailRow}>
            <Ionicons name="call-outline" size={16} color={colors.textSecondary} />
            <Text style={[styles.detailText, { color: colors.textSecondary }]}>
              {provider.phone}
            </Text>
          </View>
        </View>

        <View style={styles.providerFooter}>
          <View style={[
            styles.priceRangeBadge,
            { backgroundColor: getPriceRangeColor(provider.priceRange, colors) + '20' }
          ]}>
            <Text style={[
              styles.priceRangeText,
              { color: getPriceRangeColor(provider.priceRange, colors) }
            ]}>
              {provider.priceRange.charAt(0).toUpperCase() + provider.priceRange.slice(1)}
            </Text>
          </View>
          
          {provider.isVerified && (
            <View style={styles.verifiedBadge}>
              <Ionicons name="checkmark-circle" size={16} color={colors.success} />
              <Text style={[styles.verifiedText, { color: colors.success }]}>
                Verified
              </Text>
            </View>
          )}
        </View>
      </Card>
    );
  };

  const getPriceRangeColor = (priceRange: string, colors: any) => {
    switch (priceRange) {
      case 'premium':
        return colors.primary;
      case 'mid-range':
        return colors.warning;
      case 'budget':
        return colors.success;
      default:
        return colors.textSecondary;
    }
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
          Finding service providers...
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={[styles.title, { color: colors.text }]}>
          Choose Service Provider
        </Text>
        <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
          Select a verified service provider near you
        </Text>
      </View>

      {/* Providers List */}
      <FlatList
        data={providers}
        renderItem={renderProviderCard}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
      />

      {/* Continue Button */}
      <View style={styles.footer}>
        <Button
          title="Continue"
          onPress={handleContinue}
          disabled={!selectedProvider}
          fullWidth
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: Typography.fontSize.base,
  },
  header: {
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.xl,
  },
  title: {
    fontSize: Typography.fontSize['2xl'],
    fontWeight: Typography.fontWeight.bold,
    marginBottom: Spacing.sm,
  },
  subtitle: {
    fontSize: Typography.fontSize.base,
    lineHeight: Typography.lineHeight.lg,
  },
  listContainer: {
    paddingHorizontal: Spacing.lg,
    gap: Spacing.base,
  },
  providerCard: {
    borderRadius: BorderRadius.lg,
  },
  providerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: Spacing.base,
  },
  providerInfo: {
    flex: 1,
  },
  providerName: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
    marginBottom: Spacing.xs / 2,
  },
  providerContact: {
    fontSize: Typography.fontSize.sm,
  },
  providerDetails: {
    gap: Spacing.xs,
    marginBottom: Spacing.base,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
  },
  detailText: {
    fontSize: Typography.fontSize.sm,
    flex: 1,
  },
  providerFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: Spacing.sm,
    borderTopWidth: 1,
    borderTopColor: '#E9ECEF',
  },
  priceRangeBadge: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs / 2,
    borderRadius: BorderRadius.sm,
  },
  priceRangeText: {
    fontSize: Typography.fontSize.xs,
    fontWeight: Typography.fontWeight.medium,
  },
  verifiedBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.xs / 2,
  },
  verifiedText: {
    fontSize: Typography.fontSize.xs,
    fontWeight: Typography.fontWeight.medium,
  },
  footer: {
    padding: Spacing.lg,
    borderTopWidth: 1,
    borderTopColor: '#E9ECEF',
  },
});
