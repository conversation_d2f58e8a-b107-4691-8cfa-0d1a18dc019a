import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router, useLocalSearchParams } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

import { Colors } from '@/constants/Colors';
import { Typography, Spacing, BorderRadius } from '@/constants/Theme';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Card, Button } from '@/components/ui';
import { useApp } from '@/contexts';

export default function BookingDetailsScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const { state } = useApp();
  const { bookingId } = useLocalSearchParams<{ bookingId: string }>();

  const booking = state.recentBookings.find(b => b.id === bookingId);

  if (!booking) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: colors.error }]}>
            Booking not found
          </Text>
          <Button title="Go Back" onPress={() => router.back()} />
        </View>
      </SafeAreaView>
    );
  }

  const handleBackPress = () => {
    router.back();
  };

  const handleCancelBooking = () => {
    Alert.alert(
      'Cancel Booking',
      'Are you sure you want to cancel this booking?',
      [
        { text: 'No', style: 'cancel' },
        { text: 'Yes, Cancel', style: 'destructive' },
      ]
    );
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return colors.success;
      case 'confirmed':
      case 'in_progress':
        return colors.info;
      case 'cancelled':
        return colors.error;
      default:
        return colors.warning;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return 'checkmark-circle';
      case 'confirmed':
        return 'time';
      case 'in_progress':
        return 'play-circle';
      case 'cancelled':
        return 'close-circle';
      default:
        return 'time';
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={handleBackPress} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>
          Booking Details
        </Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Status Card */}
        <Card variant="elevated" padding="lg" style={styles.statusCard}>
          <View style={styles.statusHeader}>
            <View style={[styles.statusIcon, { backgroundColor: getStatusColor(booking.status) + '20' }]}>
              <Ionicons
                name={getStatusIcon(booking.status) as any}
                size={32}
                color={getStatusColor(booking.status)}
              />
            </View>
            <View style={styles.statusInfo}>
              <Text style={[styles.statusText, { color: getStatusColor(booking.status) }]}>
                {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
              </Text>
              <Text style={[styles.bookingId, { color: colors.textSecondary }]}>
                Booking #{booking.id.slice(-6).toUpperCase()}
              </Text>
            </View>
          </View>
        </Card>

        {/* Service Details */}
        <Card variant="outlined" padding="lg" style={styles.detailCard}>
          <Text style={[styles.cardTitle, { color: colors.text }]}>
            Service Details
          </Text>
          
          <View style={styles.detailRow}>
            <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
              Service Type:
            </Text>
            <Text style={[styles.detailValue, { color: colors.text }]}>
              {booking.serviceType === 'emergency' ? 'Emergency Service' : 'Scheduled Service'}
            </Text>
          </View>

          <View style={styles.detailRow}>
            <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
              Date Created:
            </Text>
            <Text style={[styles.detailValue, { color: colors.text }]}>
              {new Date(booking.createdAt).toLocaleDateString()}
            </Text>
          </View>

          {booking.scheduledDateTime && (
            <View style={styles.detailRow}>
              <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
                Scheduled Time:
              </Text>
              <Text style={[styles.detailValue, { color: colors.text }]}>
                {booking.scheduledDateTime}
              </Text>
            </View>
          )}
        </Card>

        {/* Vehicle Information */}
        {booking.vehicle && (
          <Card variant="outlined" padding="lg" style={styles.detailCard}>
            <Text style={[styles.cardTitle, { color: colors.text }]}>
              Vehicle Information
            </Text>
            
            <View style={styles.detailRow}>
              <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
                Vehicle:
              </Text>
              <Text style={[styles.detailValue, { color: colors.text }]}>
                {booking.vehicle.make} {booking.vehicle.model}
              </Text>
            </View>

            <View style={styles.detailRow}>
              <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
                Registration:
              </Text>
              <Text style={[styles.detailValue, { color: colors.text }]}>
                {booking.vehicle.registrationNumber}
              </Text>
            </View>

            <View style={styles.detailRow}>
              <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
                Year:
              </Text>
              <Text style={[styles.detailValue, { color: colors.text }]}>
                {booking.vehicle.year}
              </Text>
            </View>
          </Card>
        )}

        {/* Service Provider */}
        {booking.serviceProvider && (
          <Card variant="outlined" padding="lg" style={styles.detailCard}>
            <Text style={[styles.cardTitle, { color: colors.text }]}>
              Service Provider
            </Text>
            
            <View style={styles.detailRow}>
              <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
                Business:
              </Text>
              <Text style={[styles.detailValue, { color: colors.text }]}>
                {booking.serviceProvider.businessName}
              </Text>
            </View>

            <View style={styles.detailRow}>
              <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
                Contact:
              </Text>
              <Text style={[styles.detailValue, { color: colors.text }]}>
                {booking.serviceProvider.contactPerson}
              </Text>
            </View>

            <View style={styles.detailRow}>
              <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
                Phone:
              </Text>
              <Text style={[styles.detailValue, { color: colors.text }]}>
                {booking.serviceProvider.phone}
              </Text>
            </View>
          </Card>
        )}

        {/* Location */}
        {booking.location && (
          <Card variant="outlined" padding="lg" style={styles.detailCard}>
            <Text style={[styles.cardTitle, { color: colors.text }]}>
              Service Location
            </Text>
            
            <View style={styles.detailRow}>
              <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
                Address:
              </Text>
              <Text style={[styles.detailValue, { color: colors.text }]}>
                {booking.location.address}
              </Text>
            </View>
          </Card>
        )}

        {/* Cost Information */}
        <Card variant="outlined" padding="lg" style={styles.detailCard}>
          <Text style={[styles.cardTitle, { color: colors.text }]}>
            Cost Information
          </Text>
          
          <View style={styles.detailRow}>
            <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
              Total Amount:
            </Text>
            <Text style={[styles.totalAmount, { color: colors.primary }]}>
              £{booking.totalAmount.toFixed(2)}
            </Text>
          </View>
        </Card>

        {/* Notes */}
        {booking.notes && (
          <Card variant="outlined" padding="lg" style={styles.detailCard}>
            <Text style={[styles.cardTitle, { color: colors.text }]}>
              Additional Notes
            </Text>
            <Text style={[styles.notesText, { color: colors.textSecondary }]}>
              {booking.notes}
            </Text>
          </Card>
        )}
      </ScrollView>

      {/* Action Buttons */}
      {booking.status === 'pending' && (
        <View style={styles.footer}>
          <Button
            title="Cancel Booking"
            onPress={handleCancelBooking}
            variant="outline"
            style={[styles.actionButton, { borderColor: colors.error }]}
            textStyle={{ color: colors.error }}
          />
        </View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing.xl,
  },
  errorText: {
    fontSize: Typography.fontSize.lg,
    marginBottom: Spacing.lg,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.base,
  },
  backButton: {
    padding: Spacing.sm,
  },
  headerTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
  },
  placeholder: {
    width: 40,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: Spacing.lg,
  },
  statusCard: {
    marginBottom: Spacing.lg,
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.base,
  },
  statusInfo: {
    flex: 1,
  },
  statusText: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.semibold,
    marginBottom: Spacing.xs / 2,
  },
  bookingId: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.medium,
  },
  detailCard: {
    marginBottom: Spacing.base,
  },
  cardTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
    marginBottom: Spacing.base,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  detailLabel: {
    fontSize: Typography.fontSize.sm,
    flex: 1,
  },
  detailValue: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.medium,
    flex: 1,
    textAlign: 'right',
  },
  totalAmount: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.bold,
  },
  notesText: {
    fontSize: Typography.fontSize.base,
    lineHeight: Typography.lineHeight.lg,
  },
  footer: {
    padding: Spacing.lg,
    borderTopWidth: 1,
    borderTopColor: '#E9ECEF',
  },
  actionButton: {
    marginBottom: Spacing.sm,
  },
});
