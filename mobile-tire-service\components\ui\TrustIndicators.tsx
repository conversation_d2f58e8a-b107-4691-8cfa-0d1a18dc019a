import React from 'react';
import { View, Text, StyleSheet, ViewStyle } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '@/constants/Colors';
import { Typography, Spacing, BorderRadius, Shadows } from '@/constants/Theme';
import { useColorScheme } from '@/hooks/useColorScheme';

// Security Badge Component
interface SecurityBadgeProps {
  features: ('insured' | 'licensed' | 'background-checked' | 'certified')[];
  style?: ViewStyle;
}

export const SecurityBadge: React.FC<SecurityBadgeProps> = ({ features, style }) => {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  const getFeatureConfig = (feature: string) => {
    switch (feature) {
      case 'insured':
        return { icon: 'shield-checkmark', text: 'Insured', color: colors.success };
      case 'licensed':
        return { icon: 'document-text', text: 'Licensed', color: colors.info };
      case 'background-checked':
        return { icon: 'person-circle', text: 'Background Checked', color: colors.primary };
      case 'certified':
        return { icon: 'ribbon', text: 'Certified', color: colors.premium };
      default:
        return { icon: 'checkmark-circle', text: feature, color: colors.success };
    }
  };

  return (
    <View style={[styles.securityContainer, style]}>
      <Text style={[styles.securityTitle, { color: colors.text }]}>
        Trust & Safety
      </Text>
      <View style={styles.securityFeatures}>
        {features.map((feature, index) => {
          const config = getFeatureConfig(feature);
          return (
            <View key={index} style={[styles.securityFeature, { backgroundColor: config.color + '15' }]}>
              <Ionicons name={config.icon as any} size={16} color={config.color} />
              <Text style={[styles.securityFeatureText, { color: config.color }]}>
                {config.text}
              </Text>
            </View>
          );
        })}
      </View>
    </View>
  );
};

// Service Guarantee Component
interface ServiceGuaranteeProps {
  guarantees: ('satisfaction' | 'warranty' | 'price-match' | 'on-time')[];
  style?: ViewStyle;
}

export const ServiceGuarantee: React.FC<ServiceGuaranteeProps> = ({ guarantees, style }) => {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  const getGuaranteeConfig = (guarantee: string) => {
    switch (guarantee) {
      case 'satisfaction':
        return { icon: 'happy', text: '100% Satisfaction', description: 'Money back guarantee' };
      case 'warranty':
        return { icon: 'construct', text: 'Service Warranty', description: 'Up to 12 months coverage' };
      case 'price-match':
        return { icon: 'pricetag', text: 'Price Match', description: 'Best price guaranteed' };
      case 'on-time':
        return { icon: 'time', text: 'On-Time Service', description: 'Punctual arrival guaranteed' };
      default:
        return { icon: 'checkmark-circle', text: guarantee, description: '' };
    }
  };

  return (
    <View style={[styles.guaranteeContainer, { backgroundColor: colors.success + '10' }, style]}>
      <View style={styles.guaranteeHeader}>
        <Ionicons name="shield-checkmark" size={20} color={colors.success} />
        <Text style={[styles.guaranteeTitle, { color: colors.success }]}>
          Our Guarantees
        </Text>
      </View>
      <View style={styles.guaranteeList}>
        {guarantees.map((guarantee, index) => {
          const config = getGuaranteeConfig(guarantee);
          return (
            <View key={index} style={styles.guaranteeItem}>
              <Ionicons name={config.icon as any} size={16} color={colors.success} />
              <View style={styles.guaranteeText}>
                <Text style={[styles.guaranteeItemTitle, { color: colors.text }]}>
                  {config.text}
                </Text>
                {config.description && (
                  <Text style={[styles.guaranteeItemDescription, { color: colors.textTertiary }]}>
                    {config.description}
                  </Text>
                )}
              </View>
            </View>
          );
        })}
      </View>
    </View>
  );
};

// Customer Reviews Summary Component
interface ReviewsSummaryProps {
  averageRating: number;
  totalReviews: number;
  recentReviews: Array<{
    rating: number;
    comment: string;
    customerName: string;
    date: string;
  }>;
  style?: ViewStyle;
}

export const ReviewsSummary: React.FC<ReviewsSummaryProps> = ({
  averageRating,
  totalReviews,
  recentReviews,
  style,
}) => {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  const renderStars = (rating: number) => {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push(
        <Ionicons
          key={i}
          name={i <= rating ? 'star' : 'star-outline'}
          size={16}
          color={colors.warning}
        />
      );
    }
    return stars;
  };

  return (
    <View style={[styles.reviewsContainer, style]}>
      <View style={styles.reviewsHeader}>
        <View style={styles.ratingOverview}>
          <Text style={[styles.averageRating, { color: colors.text }]}>
            {averageRating.toFixed(1)}
          </Text>
          <View style={styles.starsContainer}>
            {renderStars(Math.round(averageRating))}
          </View>
          <Text style={[styles.totalReviews, { color: colors.textTertiary }]}>
            ({totalReviews} reviews)
          </Text>
        </View>
      </View>

      <View style={styles.recentReviewsList}>
        {recentReviews.slice(0, 2).map((review, index) => (
          <View key={index} style={[styles.reviewItem, { borderBottomColor: colors.borderLight }]}>
            <View style={styles.reviewHeader}>
              <View style={styles.reviewStars}>
                {renderStars(review.rating)}
              </View>
              <Text style={[styles.reviewDate, { color: colors.textTertiary }]}>
                {review.date}
              </Text>
            </View>
            <Text style={[styles.reviewComment, { color: colors.textSecondary }]} numberOfLines={2}>
              "{review.comment}"
            </Text>
            <Text style={[styles.reviewCustomer, { color: colors.textTertiary }]}>
              - {review.customerName}
            </Text>
          </View>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  securityContainer: {
    marginBottom: Spacing.lg,
  },
  securityTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
    marginBottom: Spacing.md,
  },
  securityFeatures: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.sm,
  },
  securityFeature: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.full,
  },
  securityFeatureText: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.medium,
    marginLeft: Spacing.xs,
  },
  guaranteeContainer: {
    padding: Spacing.lg,
    borderRadius: BorderRadius.lg,
    marginBottom: Spacing.lg,
    ...Shadows.sm,
  },
  guaranteeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  guaranteeTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
    marginLeft: Spacing.sm,
  },
  guaranteeList: {
    gap: Spacing.md,
  },
  guaranteeItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  guaranteeText: {
    flex: 1,
    marginLeft: Spacing.sm,
  },
  guaranteeItemTitle: {
    fontSize: Typography.fontSize.md,
    fontWeight: Typography.fontWeight.medium,
  },
  guaranteeItemDescription: {
    fontSize: Typography.fontSize.sm,
    marginTop: 2,
  },
  reviewsContainer: {
    marginBottom: Spacing.lg,
  },
  reviewsHeader: {
    marginBottom: Spacing.md,
  },
  ratingOverview: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  averageRating: {
    fontSize: Typography.fontSize['2xl'],
    fontWeight: Typography.fontWeight.bold,
    marginRight: Spacing.sm,
  },
  starsContainer: {
    flexDirection: 'row',
    marginRight: Spacing.sm,
  },
  totalReviews: {
    fontSize: Typography.fontSize.sm,
  },
  recentReviewsList: {
    gap: Spacing.md,
  },
  reviewItem: {
    paddingBottom: Spacing.md,
    borderBottomWidth: 1,
  },
  reviewHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.xs,
  },
  reviewStars: {
    flexDirection: 'row',
  },
  reviewDate: {
    fontSize: Typography.fontSize.xs,
  },
  reviewComment: {
    fontSize: Typography.fontSize.sm,
    lineHeight: Typography.lineHeight.sm,
    marginBottom: Spacing.xs,
  },
  reviewCustomer: {
    fontSize: Typography.fontSize.xs,
    fontStyle: 'italic',
  },
});
