import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ActivityIndicator,
  ViewStyle,
} from 'react-native';
import { Colors } from '@/constants/Colors';
import { Typography, Spacing } from '@/constants/Theme';
import { useColorScheme } from '@/hooks/useColorScheme';

interface LoadingProps {
  size?: 'small' | 'large';
  text?: string;
  overlay?: boolean;
  style?: ViewStyle;
}

export const Loading: React.FC<LoadingProps> = ({
  size = 'large',
  text,
  overlay = false,
  style,
}) => {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  const containerStyle = [
    overlay ? styles.overlay : styles.container,
    { backgroundColor: overlay ? 'rgba(0, 0, 0, 0.5)' : colors.background },
    style,
  ];

  return (
    <View style={containerStyle}>
      <View style={[styles.content, { backgroundColor: overlay ? colors.surface : 'transparent' }]}>
        <ActivityIndicator
          size={size}
          color={colors.primary}
          style={styles.indicator}
        />
        {text && (
          <Text style={[styles.text, { color: colors.text }]}>
            {text}
          </Text>
        )}
      </View>
    </View>
  );
};

// Full screen loading component
export const FullScreenLoading: React.FC<{ text?: string }> = ({ text = 'Loading...' }) => {
  return <Loading overlay text={text} />;
};

// Inline loading component
export const InlineLoading: React.FC<{ text?: string; size?: 'small' | 'large' }> = ({ 
  text, 
  size = 'small' 
}) => {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  return (
    <View style={styles.inlineContainer}>
      <ActivityIndicator size={size} color={colors.primary} />
      {text && (
        <Text style={[styles.inlineText, { color: colors.textSecondary }]}>
          {text}
        </Text>
      )}
    </View>
  );
};

// Loading skeleton for list items
export const LoadingSkeleton: React.FC<{ lines?: number; style?: ViewStyle }> = ({ 
  lines = 3, 
  style 
}) => {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  return (
    <View style={[styles.skeletonContainer, style]}>
      {Array.from({ length: lines }).map((_, index) => (
        <View
          key={index}
          style={[
            styles.skeletonLine,
            { backgroundColor: colors.border },
            index === lines - 1 && { width: '70%' }, // Last line shorter
          ]}
        />
      ))}
    </View>
  );
};

// Loading dots animation
export const LoadingDots: React.FC<{ color?: string }> = ({ color }) => {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const dotColor = color || colors.primary;

  return (
    <View style={styles.dotsContainer}>
      <View style={[styles.dot, { backgroundColor: dotColor }]} />
      <View style={[styles.dot, { backgroundColor: dotColor }]} />
      <View style={[styles.dot, { backgroundColor: dotColor }]} />
    </View>
  );
};

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    padding: Spacing.xl,
    borderRadius: 12,
    alignItems: 'center',
    minWidth: 120,
  },
  indicator: {
    marginBottom: Spacing.base,
  },
  text: {
    fontSize: Typography.fontSize.base,
    textAlign: 'center',
  },
  inlineContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: Spacing.base,
  },
  inlineText: {
    fontSize: Typography.fontSize.sm,
    marginLeft: Spacing.sm,
  },
  skeletonContainer: {
    padding: Spacing.base,
  },
  skeletonLine: {
    height: 16,
    borderRadius: 8,
    marginBottom: Spacing.sm,
  },
  dotsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginHorizontal: 2,
  },
});
