import {
  Booking,
  BookingFlow,
  Location,
  ServiceProvider,
  TireBrand,
  TireService,
  Vehicle,
} from "@/types";
import AsyncStorage from "@react-native-async-storage/async-storage";
import React, { createContext, useContext, useEffect, useReducer } from "react";

// Services (commented out until SQLite is properly installed)
// import { databaseService } from '@/services/DatabaseService';
// import { preferencesService } from '@/services/PreferencesService';

// App State
interface AppState {
  isLoading: boolean;
  bookingFlow: BookingFlow;
  recentBookings: Booking[];
  favoriteProviders: ServiceProvider[];
  savedLocations: Location[];
  userVehicles: Vehicle[];
  tireBrands: TireBrand[];
  tireServices: TireService[];
  error: string | null;
}

// App Actions
type AppAction =
  | { type: "SET_LOADING"; payload: boolean }
  | { type: "SET_ERROR"; payload: string }
  | { type: "CLEAR_ERROR" }
  | { type: "UPDATE_BOOKING_FLOW"; payload: Partial<BookingFlow> }
  | { type: "RESET_BOOKING_FLOW" }
  | { type: "SET_RECENT_BOOKINGS"; payload: Booking[] }
  | { type: "ADD_BOOKING"; payload: Booking }
  | {
      type: "UPDATE_BOOKING";
      payload: { id: string; updates: Partial<Booking> };
    }
  | { type: "SET_FAVORITE_PROVIDERS"; payload: ServiceProvider[] }
  | { type: "ADD_FAVORITE_PROVIDER"; payload: ServiceProvider }
  | { type: "REMOVE_FAVORITE_PROVIDER"; payload: string }
  | { type: "SET_SAVED_LOCATIONS"; payload: Location[] }
  | { type: "ADD_SAVED_LOCATION"; payload: Location }
  | { type: "REMOVE_SAVED_LOCATION"; payload: string }
  | { type: "SET_USER_VEHICLES"; payload: Vehicle[] }
  | { type: "ADD_USER_VEHICLE"; payload: Vehicle }
  | { type: "SET_TIRE_BRANDS"; payload: TireBrand[] }
  | { type: "SET_TIRE_SERVICES"; payload: TireService[] };

// App Context Type
interface AppContextType {
  state: AppState;
  updateBookingFlow: (updates: Partial<BookingFlow>) => void;
  resetBookingFlow: () => void;
  addBooking: (booking: Booking) => Promise<void>;
  updateBooking: (id: string, updates: Partial<Booking>) => Promise<void>;
  loadRecentBookings: () => Promise<void>;
  addFavoriteProvider: (provider: ServiceProvider) => Promise<void>;
  removeFavoriteProvider: (providerId: string) => Promise<void>;
  loadFavoriteProviders: () => Promise<void>;
  addSavedLocation: (location: Location) => Promise<void>;
  removeSavedLocation: (locationId: string) => Promise<void>;
  loadSavedLocations: () => Promise<void>;
  addUserVehicle: (vehicle: Vehicle) => Promise<void>;
  loadUserVehicles: () => Promise<void>;
  loadTireBrands: () => Promise<void>;
  loadTireServices: () => Promise<void>;
  clearError: () => void;
}

// Initial State
const initialBookingFlow: BookingFlow = {
  step: 1,
};

const initialState: AppState = {
  isLoading: false,
  bookingFlow: initialBookingFlow,
  recentBookings: [],
  favoriteProviders: [],
  savedLocations: [],
  userVehicles: [],
  tireBrands: [],
  tireServices: [],
  error: null,
};

// App Reducer
const appReducer = (state: AppState, action: AppAction): AppState => {
  switch (action.type) {
    case "SET_LOADING":
      return { ...state, isLoading: action.payload };

    case "SET_ERROR":
      return { ...state, error: action.payload, isLoading: false };

    case "CLEAR_ERROR":
      return { ...state, error: null };

    case "UPDATE_BOOKING_FLOW":
      return {
        ...state,
        bookingFlow: { ...state.bookingFlow, ...action.payload },
      };

    case "RESET_BOOKING_FLOW":
      return { ...state, bookingFlow: initialBookingFlow };

    case "SET_RECENT_BOOKINGS":
      return { ...state, recentBookings: action.payload };

    case "ADD_BOOKING":
      return {
        ...state,
        recentBookings: [action.payload, ...state.recentBookings],
      };

    case "UPDATE_BOOKING":
      return {
        ...state,
        recentBookings: state.recentBookings.map((booking) =>
          booking.id === action.payload.id
            ? { ...booking, ...action.payload.updates }
            : booking
        ),
      };

    case "SET_FAVORITE_PROVIDERS":
      return { ...state, favoriteProviders: action.payload };

    case "ADD_FAVORITE_PROVIDER":
      return {
        ...state,
        favoriteProviders: [...state.favoriteProviders, action.payload],
      };

    case "REMOVE_FAVORITE_PROVIDER":
      return {
        ...state,
        favoriteProviders: state.favoriteProviders.filter(
          (provider) => provider.id !== action.payload
        ),
      };

    case "SET_SAVED_LOCATIONS":
      return { ...state, savedLocations: action.payload };

    case "ADD_SAVED_LOCATION":
      return {
        ...state,
        savedLocations: [...state.savedLocations, action.payload],
      };

    case "REMOVE_SAVED_LOCATION":
      return {
        ...state,
        savedLocations: state.savedLocations.filter(
          (location) => location.id !== action.payload
        ),
      };

    case "SET_USER_VEHICLES":
      return { ...state, userVehicles: action.payload };

    case "ADD_USER_VEHICLE":
      return {
        ...state,
        userVehicles: [...state.userVehicles, action.payload],
      };

    case "SET_TIRE_BRANDS":
      return { ...state, tireBrands: action.payload };

    case "SET_TIRE_SERVICES":
      return { ...state, tireServices: action.payload };

    default:
      return state;
  }
};

// Create Context
const AppContext = createContext<AppContextType | undefined>(undefined);

// Storage Keys
const STORAGE_KEYS = {
  RECENT_BOOKINGS: "@voltifi_recent_bookings",
  FAVORITE_PROVIDERS: "@voltifi_favorite_providers",
  SAVED_LOCATIONS: "@voltifi_saved_locations",
  USER_VEHICLES: "@voltifi_user_vehicles",
};

// App Provider Component
export const AppProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // Load initial data
  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    try {
      await Promise.all([
        loadRecentBookings(),
        loadFavoriteProviders(),
        loadSavedLocations(),
        loadUserVehicles(),
        loadTireBrands(),
        loadTireServices(),
      ]);
    } catch (error) {
      console.error("Error loading initial data:", error);
    }
  };

  const updateBookingFlow = (updates: Partial<BookingFlow>) => {
    dispatch({ type: "UPDATE_BOOKING_FLOW", payload: updates });
  };

  const resetBookingFlow = () => {
    dispatch({ type: "RESET_BOOKING_FLOW" });
  };

  const addBooking = async (booking: Booking) => {
    try {
      dispatch({ type: "ADD_BOOKING", payload: booking });

      // Save to storage
      const updatedBookings = [booking, ...state.recentBookings];
      await AsyncStorage.setItem(
        STORAGE_KEYS.RECENT_BOOKINGS,
        JSON.stringify(updatedBookings.slice(0, 10)) // Keep only last 10
      );
    } catch (error) {
      dispatch({ type: "SET_ERROR", payload: "Failed to save booking" });
    }
  };

  const updateBooking = async (id: string, updates: Partial<Booking>) => {
    try {
      dispatch({ type: "UPDATE_BOOKING", payload: { id, updates } });

      // Update storage
      const updatedBookings = state.recentBookings.map((booking) =>
        booking.id === id ? { ...booking, ...updates } : booking
      );
      await AsyncStorage.setItem(
        STORAGE_KEYS.RECENT_BOOKINGS,
        JSON.stringify(updatedBookings)
      );
    } catch (error) {
      dispatch({ type: "SET_ERROR", payload: "Failed to update booking" });
    }
  };

  const loadRecentBookings = async () => {
    try {
      const bookingsJson = await AsyncStorage.getItem(
        STORAGE_KEYS.RECENT_BOOKINGS
      );
      const bookings = bookingsJson ? JSON.parse(bookingsJson) : [];
      dispatch({ type: "SET_RECENT_BOOKINGS", payload: bookings });
    } catch (error) {
      console.error("Error loading recent bookings:", error);
    }
  };

  const addFavoriteProvider = async (provider: ServiceProvider) => {
    try {
      dispatch({ type: "ADD_FAVORITE_PROVIDER", payload: provider });

      const updatedFavorites = [...state.favoriteProviders, provider];
      await AsyncStorage.setItem(
        STORAGE_KEYS.FAVORITE_PROVIDERS,
        JSON.stringify(updatedFavorites)
      );
    } catch (error) {
      dispatch({
        type: "SET_ERROR",
        payload: "Failed to add favorite provider",
      });
    }
  };

  const removeFavoriteProvider = async (providerId: string) => {
    try {
      dispatch({ type: "REMOVE_FAVORITE_PROVIDER", payload: providerId });

      const updatedFavorites = state.favoriteProviders.filter(
        (provider) => provider.id !== providerId
      );
      await AsyncStorage.setItem(
        STORAGE_KEYS.FAVORITE_PROVIDERS,
        JSON.stringify(updatedFavorites)
      );
    } catch (error) {
      dispatch({
        type: "SET_ERROR",
        payload: "Failed to remove favorite provider",
      });
    }
  };

  const loadFavoriteProviders = async () => {
    try {
      const providersJson = await AsyncStorage.getItem(
        STORAGE_KEYS.FAVORITE_PROVIDERS
      );
      const providers = providersJson ? JSON.parse(providersJson) : [];
      dispatch({ type: "SET_FAVORITE_PROVIDERS", payload: providers });
    } catch (error) {
      console.error("Error loading favorite providers:", error);
    }
  };

  const addSavedLocation = async (location: Location) => {
    try {
      dispatch({ type: "ADD_SAVED_LOCATION", payload: location });

      const updatedLocations = [...state.savedLocations, location];
      await AsyncStorage.setItem(
        STORAGE_KEYS.SAVED_LOCATIONS,
        JSON.stringify(updatedLocations)
      );
    } catch (error) {
      dispatch({ type: "SET_ERROR", payload: "Failed to save location" });
    }
  };

  const removeSavedLocation = async (locationId: string) => {
    try {
      dispatch({ type: "REMOVE_SAVED_LOCATION", payload: locationId });

      const updatedLocations = state.savedLocations.filter(
        (location) => location.id !== locationId
      );
      await AsyncStorage.setItem(
        STORAGE_KEYS.SAVED_LOCATIONS,
        JSON.stringify(updatedLocations)
      );
    } catch (error) {
      dispatch({ type: "SET_ERROR", payload: "Failed to remove location" });
    }
  };

  const loadSavedLocations = async () => {
    try {
      const locationsJson = await AsyncStorage.getItem(
        STORAGE_KEYS.SAVED_LOCATIONS
      );
      const locations = locationsJson ? JSON.parse(locationsJson) : [];
      dispatch({ type: "SET_SAVED_LOCATIONS", payload: locations });
    } catch (error) {
      console.error("Error loading saved locations:", error);
    }
  };

  const addUserVehicle = async (vehicle: Vehicle) => {
    try {
      dispatch({ type: "ADD_USER_VEHICLE", payload: vehicle });

      const updatedVehicles = [...state.userVehicles, vehicle];
      await AsyncStorage.setItem(
        STORAGE_KEYS.USER_VEHICLES,
        JSON.stringify(updatedVehicles)
      );
    } catch (error) {
      dispatch({ type: "SET_ERROR", payload: "Failed to save vehicle" });
    }
  };

  const loadUserVehicles = async () => {
    try {
      const vehiclesJson = await AsyncStorage.getItem(
        STORAGE_KEYS.USER_VEHICLES
      );
      const vehicles = vehiclesJson ? JSON.parse(vehiclesJson) : [];
      dispatch({ type: "SET_USER_VEHICLES", payload: vehicles });
    } catch (error) {
      console.error("Error loading user vehicles:", error);
    }
  };

  const loadTireBrands = async () => {
    try {
      // TODO: Replace with actual API call
      // Mock data for now
      const mockBrands: TireBrand[] = [
        { id: "1", name: "Michelin", category: "premium" },
        { id: "2", name: "Bridgestone", category: "premium" },
        { id: "3", name: "Continental", category: "mid-range" },
        { id: "4", name: "Goodyear", category: "mid-range" },
        { id: "5", name: "Hankook", category: "budget" },
        { id: "6", name: "Kumho", category: "budget" },
      ];

      dispatch({ type: "SET_TIRE_BRANDS", payload: mockBrands });
    } catch (error) {
      console.error("Error loading tire brands:", error);
    }
  };

  const loadTireServices = async () => {
    try {
      // TODO: Replace with actual API call
      // Mock data for now
      const mockServices: TireService[] = [
        {
          id: "1",
          name: "Tire Replacement",
          description: "Complete tire replacement service",
          basePrice: 80,
          estimatedDuration: 45,
          category: "scheduled",
        },
        {
          id: "2",
          name: "Emergency Tire Change",
          description: "Emergency roadside tire change",
          basePrice: 120,
          estimatedDuration: 30,
          category: "emergency",
        },
        {
          id: "3",
          name: "Tire Repair",
          description: "Puncture repair service",
          basePrice: 25,
          estimatedDuration: 20,
          category: "scheduled",
        },
      ];

      dispatch({ type: "SET_TIRE_SERVICES", payload: mockServices });
    } catch (error) {
      console.error("Error loading tire services:", error);
    }
  };

  const clearError = () => {
    dispatch({ type: "CLEAR_ERROR" });
  };

  const value: AppContextType = {
    state,
    updateBookingFlow,
    resetBookingFlow,
    addBooking,
    updateBooking,
    loadRecentBookings,
    addFavoriteProvider,
    removeFavoriteProvider,
    loadFavoriteProviders,
    addSavedLocation,
    removeSavedLocation,
    loadSavedLocations,
    addUserVehicle,
    loadUserVehicles,
    loadTireBrands,
    loadTireServices,
    clearError,
  };

  return <AppContext.Provider value={value}>{children}</AppContext.Provider>;
};

// Custom hook to use app context
export const useApp = () => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error("useApp must be used within an AppProvider");
  }
  return context;
};
