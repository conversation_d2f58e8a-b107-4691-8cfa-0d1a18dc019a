import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router, useLocalSearchParams } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

import { Colors } from '@/constants/Colors';
import { Typography, Spacing } from '@/constants/Theme';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Button, Stepper, ProgressBar } from '@/components/ui';
import { useApp } from '@/contexts';

// Import step components
import ServiceTypeStep from '@/components/booking/ServiceTypeStep';
import VehicleStep from '@/components/booking/VehicleStep';
import TireBrandStep from '@/components/booking/TireBrandStep';
import ServiceProviderStep from '@/components/booking/ServiceProviderStep';
import LocationStep from '@/components/booking/LocationStep';
import ConfirmationStep from '@/components/booking/ConfirmationStep';

const BOOKING_STEPS = [
  { id: '1', title: 'Service Type', description: 'Choose your service type' },
  { id: '2', title: 'Vehicle', description: 'Enter vehicle details' },
  { id: '3', title: 'Tire Brand', description: 'Select tire brand' },
  { id: '4', title: 'Provider', description: 'Choose service provider' },
  { id: '5', title: 'Location', description: 'Set location & time' },
  { id: '6', title: 'Confirm', description: 'Review and confirm' },
];

export default function BookingFlowScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const { state, updateBookingFlow, resetBookingFlow } = useApp();
  const { step: initialStep } = useLocalSearchParams<{ step?: string }>();

  const [currentStep, setCurrentStep] = useState(
    initialStep ? parseInt(initialStep) : state.bookingFlow.step || 1
  );
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);

  useEffect(() => {
    // Update booking flow state when step changes
    updateBookingFlow({ step: currentStep });
  }, [currentStep]);

  const handleNext = () => {
    if (currentStep < BOOKING_STEPS.length) {
      setCompletedSteps(prev => [...prev, currentStep]);
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleStepComplete = (stepData: any) => {
    updateBookingFlow(stepData);
    handleNext();
  };

  const handleBookingComplete = () => {
    // Handle final booking submission
    Alert.alert(
      'Booking Confirmed',
      'Your tire service booking has been confirmed!',
      [
        {
          text: 'OK',
          onPress: () => {
            resetBookingFlow();
            router.replace('/(tabs)');
          },
        },
      ]
    );
  };

  const handleBackPress = () => {
    if (currentStep === 1) {
      Alert.alert(
        'Cancel Booking',
        'Are you sure you want to cancel this booking?',
        [
          { text: 'Continue Booking', style: 'cancel' },
          {
            text: 'Cancel',
            style: 'destructive',
            onPress: () => {
              resetBookingFlow();
              router.back();
            },
          },
        ]
      );
    } else {
      handlePrevious();
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <ServiceTypeStep
            onComplete={handleStepComplete}
            initialData={state.bookingFlow}
          />
        );
      case 2:
        return (
          <VehicleStep
            onComplete={handleStepComplete}
            initialData={state.bookingFlow}
          />
        );
      case 3:
        return (
          <TireBrandStep
            onComplete={handleStepComplete}
            initialData={state.bookingFlow}
          />
        );
      case 4:
        return (
          <ServiceProviderStep
            onComplete={handleStepComplete}
            initialData={state.bookingFlow}
          />
        );
      case 5:
        return (
          <LocationStep
            onComplete={handleStepComplete}
            initialData={state.bookingFlow}
          />
        );
      case 6:
        return (
          <ConfirmationStep
            onComplete={handleBookingComplete}
            bookingData={state.bookingFlow}
          />
        );
      default:
        return null;
    }
  };

  const progress = currentStep / BOOKING_STEPS.length;

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={handleBackPress} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
        
        <View style={styles.headerContent}>
          <Text style={[styles.headerTitle, { color: colors.text }]}>
            Book Service
          </Text>
          <Text style={[styles.headerSubtitle, { color: colors.textSecondary }]}>
            Step {currentStep} of {BOOKING_STEPS.length}
          </Text>
        </View>
        
        <View style={styles.placeholder} />
      </View>

      {/* Progress Bar */}
      <View style={styles.progressContainer}>
        <ProgressBar
          progress={progress}
          showPercentage={false}
          style={styles.progressBar}
        />
      </View>

      {/* Stepper */}
      <View style={styles.stepperContainer}>
        <Stepper
          steps={BOOKING_STEPS}
          currentStep={currentStep - 1} // Convert to 0-based index
          completedSteps={completedSteps.map(step => step - 1)} // Convert to 0-based
          orientation="horizontal"
          showLabels={false}
        />
      </View>

      {/* Step Content */}
      <View style={styles.content}>
        {renderStepContent()}
      </View>

      {/* Navigation Footer */}
      <View style={styles.footer}>
        <View style={styles.navigationButtons}>
          {currentStep > 1 && (
            <Button
              title="Previous"
              onPress={handlePrevious}
              variant="outline"
              style={styles.navButton}
            />
          )}
          
          <View style={styles.stepInfo}>
            <Text style={[styles.stepTitle, { color: colors.text }]}>
              {BOOKING_STEPS[currentStep - 1].title}
            </Text>
            <Text style={[styles.stepDescription, { color: colors.textSecondary }]}>
              {BOOKING_STEPS[currentStep - 1].description}
            </Text>
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.base,
  },
  backButton: {
    padding: Spacing.sm,
  },
  headerContent: {
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
  },
  headerSubtitle: {
    fontSize: Typography.fontSize.sm,
    marginTop: Spacing.xs / 2,
  },
  placeholder: {
    width: 40,
  },
  progressContainer: {
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.base,
  },
  progressBar: {
    height: 4,
  },
  stepperContainer: {
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.lg,
  },
  content: {
    flex: 1,
  },
  footer: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.base,
    borderTopWidth: 1,
    borderTopColor: '#E9ECEF',
  },
  navigationButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.base,
  },
  navButton: {
    paddingHorizontal: Spacing.lg,
  },
  stepInfo: {
    flex: 1,
    alignItems: 'center',
  },
  stepTitle: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.semibold,
  },
  stepDescription: {
    fontSize: Typography.fontSize.sm,
    marginTop: Spacing.xs / 2,
  },
});
