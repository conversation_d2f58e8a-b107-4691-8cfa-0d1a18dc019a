import { Colors } from "@/constants/Colors";
import { BorderRadius, Shadows, Spacing } from "@/constants/Theme";
import { useColorScheme } from "@/hooks/useColorScheme";
import React from "react";
import {
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ViewStyle,
} from "react-native";

interface CardProps {
  children: React.ReactNode;
  onPress?: () => void;
  variant?: "default" | "elevated" | "outlined";
  padding?: "none" | "sm" | "md" | "lg";
  margin?: "none" | "sm" | "md" | "lg";
  style?: ViewStyle;
  disabled?: boolean;
}

export const Card: React.FC<CardProps> = ({
  children,
  onPress,
  variant = "default",
  padding = "md",
  margin = "none",
  style,
  disabled = false,
}) => {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];

  const getCardStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      borderRadius: BorderRadius.lg,
      backgroundColor: colors.card,
    };

    // Padding variations
    const paddingStyles = {
      none: {},
      sm: { padding: Spacing.sm },
      md: { padding: Spacing.lg },
      lg: { padding: Spacing.xl },
    };

    // Margin variations
    const marginStyles = {
      none: {},
      sm: { margin: Spacing.sm },
      md: { margin: Spacing.md },
      lg: { margin: Spacing.lg },
    };

    // Variant styles
    const variantStyles = {
      default: {
        ...Shadows.sm,
      },
      elevated: {
        ...Shadows.md,
      },
      outlined: {
        borderWidth: 1,
        borderColor: colors.border,
        ...Shadows.none,
      },
    };

    return {
      ...baseStyle,
      ...paddingStyles[padding],
      ...marginStyles[margin],
      ...variantStyles[variant],
      opacity: disabled ? 0.6 : 1,
    };
  };

  if (onPress) {
    return (
      <TouchableOpacity
        style={[getCardStyle(), style]}
        onPress={onPress}
        disabled={disabled}
        activeOpacity={0.8}
      >
        {children}
      </TouchableOpacity>
    );
  }

  return <View style={[getCardStyle(), style]}>{children}</View>;
};

// Service Card Component for Home Screen
interface ServiceCardProps {
  title: string;
  subtitle: string;
  icon: React.ReactNode;
  onPress: () => void;
  disabled?: boolean;
  comingSoon?: boolean;
  style?: ViewStyle;
}

export const ServiceCard: React.FC<ServiceCardProps> = ({
  title,
  subtitle,
  icon,
  onPress,
  disabled = false,
  comingSoon = false,
  style,
}) => {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];

  return (
    <Card
      onPress={onPress}
      disabled={disabled || comingSoon}
      variant="elevated"
      padding="lg"
      style={[styles.serviceCard, style]}
    >
      <View style={styles.serviceCardContent}>
        <View style={styles.serviceCardIcon}>{icon}</View>
        <View style={styles.serviceCardText}>
          <Text style={[styles.serviceCardTitle, { color: colors.text }]}>
            {title}
          </Text>
          <Text
            style={[
              styles.serviceCardSubtitle,
              { color: colors.textSecondary },
            ]}
          >
            {comingSoon ? "Coming Soon" : subtitle}
          </Text>
        </View>
        {comingSoon && (
          <View
            style={[
              styles.comingSoonBadge,
              { backgroundColor: colors.warning },
            ]}
          >
            <Text style={styles.comingSoonText}>Soon</Text>
          </View>
        )}
      </View>
    </Card>
  );
};

const styles = StyleSheet.create({
  serviceCard: {
    flex: 1,
    minHeight: 120,
  },
  serviceCardContent: {
    flexDirection: "row",
    alignItems: "center",
    position: "relative",
  },
  serviceCardIcon: {
    marginRight: Spacing.md,
  },
  serviceCardText: {
    flex: 1,
  },
  serviceCardTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 4,
  },
  serviceCardSubtitle: {
    fontSize: 14,
    lineHeight: 20,
  },
  comingSoonBadge: {
    position: "absolute",
    top: -8,
    right: -8,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  comingSoonText: {
    color: "#FFFFFF",
    fontSize: 10,
    fontWeight: "600",
  },
});
