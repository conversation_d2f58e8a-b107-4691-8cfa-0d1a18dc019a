import { Colors } from "@/constants/Colors";
import { BorderRadius, Shadows, Spacing, Typography } from "@/constants/Theme";
import { useColorScheme } from "@/hooks/useColorScheme";
import React from "react";
import {
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ViewStyle,
} from "react-native";

interface CardProps {
  children: React.ReactNode;
  onPress?: () => void;
  variant?: "default" | "elevated" | "outlined" | "premium" | "service";
  size?: "compact" | "default" | "large";
  padding?: "none" | "xs" | "sm" | "md" | "lg" | "xl";
  margin?: "none" | "xs" | "sm" | "md" | "lg" | "xl";
  style?: ViewStyle;
  disabled?: boolean;
  loading?: boolean;
}

export const Card: React.FC<CardProps> = ({
  children,
  onPress,
  variant = "default",
  size = "default",
  padding = "md",
  margin = "none",
  style,
  disabled = false,
  loading = false,
}) => {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];

  const getCardStyle = (): ViewStyle => {
    // Base style with size variations
    const sizeStyles = {
      compact: {
        borderRadius: BorderRadius.md,
        minHeight: 80,
      },
      default: {
        borderRadius: BorderRadius.lg,
        minHeight: 120,
      },
      large: {
        borderRadius: BorderRadius.xl,
        minHeight: 160,
      },
    };

    const baseStyle: ViewStyle = {
      backgroundColor: colors.card,
      ...sizeStyles[size],
    };

    // Padding variations
    const paddingStyles = {
      none: {},
      xs: { padding: Spacing.xs },
      sm: { padding: Spacing.sm },
      md: { padding: Spacing.base },
      lg: { padding: Spacing.lg },
      xl: { padding: Spacing.xl },
    };

    // Margin variations
    const marginStyles = {
      none: {},
      xs: { margin: Spacing.xs },
      sm: { margin: Spacing.sm },
      md: { margin: Spacing.md },
      lg: { margin: Spacing.lg },
      xl: { margin: Spacing.xl },
    };

    // Variant styles with modern automotive feel
    const variantStyles = {
      default: {
        ...Shadows.card,
      },
      elevated: {
        ...Shadows.md,
      },
      outlined: {
        borderWidth: 1,
        borderColor: colors.border,
        ...Shadows.xs,
      },
      premium: {
        backgroundColor: colors.cardElevated,
        borderWidth: 1,
        borderColor: colors.premium + "20",
        ...Shadows.lg,
      },
      service: {
        backgroundColor: colors.card,
        borderWidth: 2,
        borderColor: colors.primary + "10",
        ...Shadows.md,
      },
    };

    return {
      ...baseStyle,
      ...paddingStyles[padding],
      ...marginStyles[margin],
      ...variantStyles[variant],
      opacity: disabled ? 0.6 : loading ? 0.8 : 1,
    };
  };

  if (onPress) {
    return (
      <TouchableOpacity
        style={[getCardStyle(), style]}
        onPress={onPress}
        disabled={disabled}
        activeOpacity={0.8}
      >
        {children}
      </TouchableOpacity>
    );
  }

  return <View style={[getCardStyle(), style]}>{children}</View>;
};

// Enhanced Service Card Component for Home Screen
interface ServiceCardProps {
  title: string;
  subtitle: string;
  icon: React.ReactNode;
  onPress: () => void;
  disabled?: boolean;
  comingSoon?: boolean;
  featured?: boolean;
  style?: ViewStyle;
}

export const ServiceCard: React.FC<ServiceCardProps> = ({
  title,
  subtitle,
  icon,
  onPress,
  disabled = false,
  comingSoon = false,
  featured = false,
  style,
}) => {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];

  const getServiceCardStyle = (): ViewStyle => ({
    flex: 1,
    minHeight: 140,
    position: "relative",
  });

  return (
    <Card
      onPress={onPress}
      disabled={disabled || comingSoon}
      variant={featured ? "premium" : "service"}
      size="default"
      padding="lg"
      style={StyleSheet.flatten([getServiceCardStyle(), style])}
    >
      <View style={styles.serviceCardContent}>
        <View
          style={[
            styles.serviceCardIcon,
            featured && { backgroundColor: colors.premium + "15" },
          ]}
        >
          {icon}
        </View>
        <View style={styles.serviceCardText}>
          <Text
            style={[
              styles.serviceCardTitle,
              {
                color: colors.text,
                fontSize: Typography.fontSize.lg,
                fontWeight: Typography.fontWeight.semibold,
                lineHeight: Typography.lineHeight.lg,
              },
            ]}
          >
            {title}
          </Text>
          <Text
            style={[
              styles.serviceCardSubtitle,
              {
                color: colors.textTertiary,
                fontSize: Typography.fontSize.sm,
                lineHeight: Typography.lineHeight.sm,
                marginTop: Spacing.xs,
              },
            ]}
          >
            {comingSoon ? "Coming Soon" : subtitle}
          </Text>
        </View>
        {comingSoon && (
          <View
            style={[
              styles.comingSoonBadge,
              { backgroundColor: colors.warning },
            ]}
          >
            <Text
              style={[
                styles.comingSoonText,
                {
                  fontSize: Typography.fontSize.xs,
                  fontWeight: Typography.fontWeight.semibold,
                },
              ]}
            >
              Soon
            </Text>
          </View>
        )}
        {featured && !comingSoon && (
          <View
            style={[styles.featuredBadge, { backgroundColor: colors.premium }]}
          >
            <Text
              style={[
                styles.featuredText,
                {
                  fontSize: Typography.fontSize.xs,
                  fontWeight: Typography.fontWeight.semibold,
                },
              ]}
            >
              Popular
            </Text>
          </View>
        )}
      </View>
    </Card>
  );
};

const styles = StyleSheet.create({
  serviceCard: {
    flex: 1,
    minHeight: 140,
  },
  serviceCardContent: {
    flexDirection: "row",
    alignItems: "flex-start",
    position: "relative",
    height: "100%",
  },
  serviceCardIcon: {
    marginRight: Spacing.md,
    padding: Spacing.sm,
    borderRadius: BorderRadius.md,
    alignItems: "center",
    justifyContent: "center",
    minWidth: 48,
    minHeight: 48,
  },
  serviceCardText: {
    flex: 1,
    paddingTop: Spacing.xs,
  },
  serviceCardTitle: {
    // Typography styles are applied inline for better control
  },
  serviceCardSubtitle: {
    // Typography styles are applied inline for better control
  },
  comingSoonBadge: {
    position: "absolute",
    top: -Spacing.sm,
    right: -Spacing.sm,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.full,
    ...Shadows.sm,
  },
  comingSoonText: {
    color: "#FFFFFF",
  },
  featuredBadge: {
    position: "absolute",
    top: -Spacing.sm,
    right: -Spacing.sm,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.full,
    ...Shadows.sm,
  },
  featuredText: {
    color: "#FFFFFF",
  },
});
