import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  FlatList,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

import { Colors } from '@/constants/Colors';
import { Typography, Spacing, BorderRadius } from '@/constants/Theme';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Card, Button } from '@/components/ui';
import { useApp } from '@/contexts';
import { ServiceProvider, Location } from '@/types';

type FavoriteTab = 'providers' | 'locations';

export default function FavoritesScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const { state, removeFavoriteProvider, removeSavedLocation } = useApp();
  const [activeTab, setActiveTab] = useState<FavoriteTab>('providers');

  const tabs: { key: FavoriteTab; label: string; icon: string }[] = [
    { key: 'providers', label: 'Service Providers', icon: 'business' },
    { key: 'locations', label: 'Saved Locations', icon: 'location' },
  ];

  const handleProviderPress = (provider: ServiceProvider) => {
    // Navigate to provider details or booking flow
    router.push(`/booking-flow?providerId=${provider.id}`);
  };

  const handleRemoveProvider = (providerId: string) => {
    removeFavoriteProvider(providerId);
  };

  const handleRemoveLocation = (locationId: string) => {
    removeSavedLocation(locationId);
  };

  const handleFindProviders = () => {
    router.push('/booking-flow');
  };

  const renderProviderItem = ({ item: provider }: { item: ServiceProvider }) => (
    <Card
      variant="outlined"
      padding="md"
      style={styles.itemCard}
      onPress={() => handleProviderPress(provider)}
    >
      <View style={styles.itemHeader}>
        <View style={styles.itemInfo}>
          <Text style={[styles.itemTitle, { color: colors.text }]}>
            {provider.businessName}
          </Text>
          <Text style={[styles.itemSubtitle, { color: colors.textSecondary }]}>
            {provider.contactPerson}
          </Text>
        </View>
        
        <TouchableOpacity
          onPress={() => handleRemoveProvider(provider.id)}
          style={styles.removeButton}
        >
          <Ionicons name="heart" size={24} color={colors.error} />
        </TouchableOpacity>
      </View>

      <View style={styles.itemDetails}>
        <View style={styles.detailRow}>
          <Ionicons name="location-outline" size={16} color={colors.textSecondary} />
          <Text style={[styles.detailText, { color: colors.textSecondary }]}>
            {provider.address}
          </Text>
        </View>
        
        <View style={styles.detailRow}>
          <Ionicons name="star" size={16} color={colors.warning} />
          <Text style={[styles.detailText, { color: colors.textSecondary }]}>
            {provider.rating.toFixed(1)} ({provider.reviewCount} reviews)
          </Text>
        </View>
        
        <View style={styles.detailRow}>
          <Ionicons name="call-outline" size={16} color={colors.textSecondary} />
          <Text style={[styles.detailText, { color: colors.textSecondary }]}>
            {provider.phone}
          </Text>
        </View>
      </View>

      <View style={styles.itemFooter}>
        <View style={[styles.priceRangeBadge, { backgroundColor: getPriceRangeColor(provider.priceRange, colors) + '20' }]}>
          <Text style={[styles.priceRangeText, { color: getPriceRangeColor(provider.priceRange, colors) }]}>
            {provider.priceRange.charAt(0).toUpperCase() + provider.priceRange.slice(1)}
          </Text>
        </View>
        {provider.isVerified && (
          <View style={styles.verifiedBadge}>
            <Ionicons name="checkmark-circle" size={16} color={colors.success} />
            <Text style={[styles.verifiedText, { color: colors.success }]}>
              Verified
            </Text>
          </View>
        )}
      </View>
    </Card>
  );

  const renderLocationItem = ({ item: location }: { item: Location }) => (
    <Card
      variant="outlined"
      padding="md"
      style={styles.itemCard}
    >
      <View style={styles.itemHeader}>
        <View style={styles.itemInfo}>
          <Text style={[styles.itemTitle, { color: colors.text }]}>
            {location.name}
          </Text>
          <Text style={[styles.itemSubtitle, { color: colors.textSecondary }]}>
            {location.address}
          </Text>
        </View>
        
        <TouchableOpacity
          onPress={() => handleRemoveLocation(location.id)}
          style={styles.removeButton}
        >
          <Ionicons name="heart" size={24} color={colors.error} />
        </TouchableOpacity>
      </View>

      <View style={styles.itemDetails}>
        <View style={styles.detailRow}>
          <Ionicons name="navigate-outline" size={16} color={colors.textSecondary} />
          <Text style={[styles.detailText, { color: colors.textSecondary }]}>
            Lat: {location.latitude.toFixed(6)}, Lng: {location.longitude.toFixed(6)}
          </Text>
        </View>
        
        {location.isDefault && (
          <View style={styles.detailRow}>
            <Ionicons name="home-outline" size={16} color={colors.primary} />
            <Text style={[styles.detailText, { color: colors.primary }]}>
              Default Location
            </Text>
          </View>
        )}
      </View>
    </Card>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons 
        name={activeTab === 'providers' ? 'business-outline' : 'location-outline'} 
        size={64} 
        color={colors.textSecondary} 
      />
      <Text style={[styles.emptyStateTitle, { color: colors.text }]}>
        {activeTab === 'providers' ? 'No favorite providers' : 'No saved locations'}
      </Text>
      <Text style={[styles.emptyStateText, { color: colors.textSecondary }]}>
        {activeTab === 'providers' 
          ? 'Add service providers to your favorites for quick access'
          : 'Save frequently used locations for faster booking'
        }
      </Text>
      {activeTab === 'providers' && (
        <Button
          title="Find Providers"
          onPress={handleFindProviders}
          style={styles.emptyStateButton}
        />
      )}
    </View>
  );

  const getPriceRangeColor = (priceRange: string, colors: any) => {
    switch (priceRange) {
      case 'premium':
        return colors.primary;
      case 'mid-range':
        return colors.warning;
      case 'budget':
        return colors.success;
      default:
        return colors.textSecondary;
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={[styles.headerTitle, { color: colors.text }]}>
          Favorites
        </Text>
      </View>

      {/* Tabs */}
      <View style={styles.tabsContainer}>
        {tabs.map((tab) => (
          <TouchableOpacity
            key={tab.key}
            style={[
              styles.tabButton,
              {
                backgroundColor: activeTab === tab.key ? colors.primary : colors.surface,
                borderColor: activeTab === tab.key ? colors.primary : colors.border,
              },
            ]}
            onPress={() => setActiveTab(tab.key)}
          >
            <Ionicons
              name={tab.icon as any}
              size={20}
              color={activeTab === tab.key ? colors.textLight : colors.textSecondary}
            />
            <Text
              style={[
                styles.tabText,
                {
                  color: activeTab === tab.key ? colors.textLight : colors.text,
                },
              ]}
            >
              {tab.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Content */}
      <FlatList
        data={activeTab === 'providers' ? state.favoriteProviders : state.savedLocations}
        renderItem={activeTab === 'providers' ? renderProviderItem : renderLocationItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={renderEmptyState}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.base,
  },
  headerTitle: {
    fontSize: Typography.fontSize['2xl'],
    fontWeight: Typography.fontWeight.bold,
  },
  tabsContainer: {
    flexDirection: 'row',
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.base,
    gap: Spacing.sm,
  },
  tabButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Spacing.base,
    paddingHorizontal: Spacing.base,
    borderRadius: BorderRadius.md,
    borderWidth: 1,
    gap: Spacing.sm,
  },
  tabText: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.medium,
  },
  listContainer: {
    padding: Spacing.lg,
    gap: Spacing.base,
  },
  itemCard: {
    borderRadius: BorderRadius.lg,
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: Spacing.base,
  },
  itemInfo: {
    flex: 1,
  },
  itemTitle: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.semibold,
    marginBottom: Spacing.xs / 2,
  },
  itemSubtitle: {
    fontSize: Typography.fontSize.sm,
  },
  removeButton: {
    padding: Spacing.xs,
  },
  itemDetails: {
    gap: Spacing.xs,
    marginBottom: Spacing.base,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
  },
  detailText: {
    fontSize: Typography.fontSize.sm,
    flex: 1,
  },
  itemFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: Spacing.sm,
    borderTopWidth: 1,
    borderTopColor: '#E9ECEF',
  },
  priceRangeBadge: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs / 2,
    borderRadius: BorderRadius.sm,
  },
  priceRangeText: {
    fontSize: Typography.fontSize.xs,
    fontWeight: Typography.fontWeight.medium,
  },
  verifiedBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.xs / 2,
  },
  verifiedText: {
    fontSize: Typography.fontSize.xs,
    fontWeight: Typography.fontWeight.medium,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: Spacing['4xl'],
    paddingHorizontal: Spacing.xl,
  },
  emptyStateTitle: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.semibold,
    marginTop: Spacing.lg,
    marginBottom: Spacing.sm,
  },
  emptyStateText: {
    fontSize: Typography.fontSize.base,
    textAlign: 'center',
    lineHeight: Typography.lineHeight.lg,
    marginBottom: Spacing.xl,
  },
  emptyStateButton: {
    paddingHorizontal: Spacing['2xl'],
  },
});
