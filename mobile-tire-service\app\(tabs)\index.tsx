import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import React from "react";
import {
  Image,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

import { Card, ServiceCard } from "@/components/ui";
import { Colors } from "@/constants/Colors";
import { BorderRadius, Spacing, Typography } from "@/constants/Theme";
import { useApp, useAuth } from "@/contexts";
import { useColorScheme } from "@/hooks/useColorScheme";

export default function HomeScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];
  const { state: authState } = useAuth();
  const { state: appState } = useApp();

  const handleTireService = () => {
    router.push("/booking-flow");
  };

  const handleEVCharging = () => {
    // Coming soon functionality
  };

  const handleViewAllBookings = () => {
    router.push("/(tabs)/bookings");
  };

  const user = authState.user;
  const recentBookings = appState.recentBookings.slice(0, 3); // Show only 3 recent bookings

  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: colors.background }]}
    >
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.userInfo}>
            <View style={styles.userAvatar}>
              <Text style={[styles.userInitials, { color: colors.textLight }]}>
                {user?.firstName?.[0]}
                {user?.lastName?.[0]}
              </Text>
            </View>
            <View style={styles.userDetails}>
              <Text style={[styles.greeting, { color: colors.textSecondary }]}>
                Good morning,
              </Text>
              <Text style={[styles.userName, { color: colors.text }]}>
                {user?.firstName} {user?.lastName}
              </Text>
            </View>
          </View>

          <TouchableOpacity style={styles.notificationButton}>
            <Ionicons
              name="notifications-outline"
              size={24}
              color={colors.text}
            />
          </TouchableOpacity>
        </View>

        {/* Welcome Card */}
        <Card
          variant="elevated"
          padding="lg"
          style={[styles.welcomeCard, { backgroundColor: colors.primary }]}
        >
          <View style={styles.welcomeContent}>
            <View style={styles.welcomeText}>
              <Text style={[styles.welcomeTitle, { color: colors.textLight }]}>
                Welcome to Voltifi
              </Text>
              <Text
                style={[styles.welcomeSubtitle, { color: colors.textLight }]}
              >
                Your mobile tire service and EV charging platform
              </Text>
            </View>
            <Image
              source={require("@/assets/images/adaptive-icon.png")}
              style={styles.welcomeImage}
              resizeMode="contain"
            />
          </View>
        </Card>

        {/* Services Section */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Our Services
          </Text>

          <View style={styles.servicesRow}>
            <ServiceCard
              title="Mobile Tire Service"
              subtitle="Professional tire services at your location"
              icon={<Ionicons name="car" size={32} color={colors.primary} />}
              onPress={handleTireService}
              style={styles.serviceCard}
            />

            <ServiceCard
              title="EV Charging"
              subtitle="Mobile charging solutions"
              icon={
                <Ionicons name="flash" size={32} color={colors.textSecondary} />
              }
              onPress={handleEVCharging}
              comingSoon={true}
              style={styles.serviceCard}
            />
          </View>
        </View>

        {/* Recent Bookings Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Recent Bookings
            </Text>
            {recentBookings.length > 0 && (
              <TouchableOpacity onPress={handleViewAllBookings}>
                <Text style={[styles.viewAllText, { color: colors.primary }]}>
                  View All
                </Text>
              </TouchableOpacity>
            )}
          </View>

          {recentBookings.length > 0 ? (
            <View style={styles.bookingsList}>
              {recentBookings.map((booking) => (
                <Card
                  key={booking.id}
                  variant="outlined"
                  padding="md"
                  style={styles.bookingCard}
                  onPress={() =>
                    router.push(`/booking-details?bookingId=${booking.id}`)
                  }
                >
                  <View style={styles.bookingContent}>
                    <View style={styles.bookingInfo}>
                      <Text
                        style={[styles.bookingTitle, { color: colors.text }]}
                      >
                        {booking.serviceType === "emergency"
                          ? "Emergency Service"
                          : "Scheduled Service"}
                      </Text>
                      <Text
                        style={[
                          styles.bookingDate,
                          { color: colors.textSecondary },
                        ]}
                      >
                        {new Date(booking.createdAt).toLocaleDateString()}
                      </Text>
                    </View>
                    <View
                      style={[
                        styles.statusBadge,
                        {
                          backgroundColor: getStatusColor(
                            booking.status,
                            colors
                          ),
                        },
                      ]}
                    >
                      <Text
                        style={[styles.statusText, { color: colors.textLight }]}
                      >
                        {booking.status.charAt(0).toUpperCase() +
                          booking.status.slice(1)}
                      </Text>
                    </View>
                  </View>
                </Card>
              ))}
            </View>
          ) : (
            <Card variant="outlined" padding="lg" style={styles.emptyState}>
              <View style={styles.emptyStateContent}>
                <Ionicons
                  name="calendar-outline"
                  size={48}
                  color={colors.textSecondary}
                />
                <Text style={[styles.emptyStateTitle, { color: colors.text }]}>
                  No bookings yet
                </Text>
                <Text
                  style={[
                    styles.emptyStateText,
                    { color: colors.textSecondary },
                  ]}
                >
                  Book your first tire service to get started
                </Text>
              </View>
            </Card>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

// Helper function to get status color
const getStatusColor = (status: string, colors: any) => {
  switch (status) {
    case "completed":
      return colors.success;
    case "in_progress":
      return colors.info;
    case "cancelled":
      return colors.error;
    default:
      return colors.warning;
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: Spacing.xl,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.base,
  },
  userInfo: {
    flexDirection: "row",
    alignItems: "center",
  },
  userAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: "#007AFF",
    justifyContent: "center",
    alignItems: "center",
    marginRight: Spacing.base,
  },
  userInitials: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
  },
  userDetails: {
    flex: 1,
  },
  greeting: {
    fontSize: Typography.fontSize.sm,
  },
  userName: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
  },
  notificationButton: {
    padding: Spacing.sm,
  },
  welcomeCard: {
    marginHorizontal: Spacing.lg,
    marginBottom: Spacing.xl,
  },
  welcomeContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  welcomeText: {
    flex: 1,
  },
  welcomeTitle: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.bold,
    marginBottom: Spacing.xs,
  },
  welcomeSubtitle: {
    fontSize: Typography.fontSize.sm,
    lineHeight: Typography.lineHeight.sm,
  },
  welcomeImage: {
    width: 60,
    height: 60,
  },
  section: {
    marginBottom: Spacing.xl,
    paddingHorizontal: Spacing.lg,
  },
  sectionHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: Spacing.base,
  },
  sectionTitle: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.semibold,
  },
  viewAllText: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.medium,
  },
  servicesRow: {
    flexDirection: "row",
    gap: Spacing.base,
  },
  serviceCard: {
    flex: 1,
  },
  bookingsList: {
    gap: Spacing.base,
  },
  bookingCard: {
    borderRadius: BorderRadius.md,
  },
  bookingContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  bookingInfo: {
    flex: 1,
  },
  bookingTitle: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.medium,
    marginBottom: Spacing.xs / 2,
  },
  bookingDate: {
    fontSize: Typography.fontSize.sm,
  },
  statusBadge: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs / 2,
    borderRadius: BorderRadius.sm,
  },
  statusText: {
    fontSize: Typography.fontSize.xs,
    fontWeight: Typography.fontWeight.medium,
  },
  emptyState: {
    alignItems: "center",
  },
  emptyStateContent: {
    alignItems: "center",
  },
  emptyStateTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
    marginTop: Spacing.base,
    marginBottom: Spacing.xs,
  },
  emptyStateText: {
    fontSize: Typography.fontSize.base,
    textAlign: "center",
  },
});
