import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { Colors } from '@/constants/Colors';
import { Typography, Spacing, BorderRadius } from '@/constants/Theme';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Card, Button, Input } from '@/components/ui';
import { BookingFlow, Location } from '@/types';
import { useApp } from '@/contexts';

interface LocationStepProps {
  onComplete: (data: Partial<BookingFlow>) => void;
  initialData: BookingFlow;
}

export default function LocationStep({ onComplete, initialData }: LocationStepProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const { state } = useApp();

  const [selectedLocation, setSelectedLocation] = useState<Location | null>(
    initialData.location || null
  );
  const [customAddress, setCustomAddress] = useState('');
  const [selectedDateTime, setSelectedDateTime] = useState<string>(
    initialData.scheduledDateTime || ''
  );
  const [notes, setNotes] = useState(initialData.notes || '');

  const isEmergencyService = initialData.serviceType === 'emergency';

  const handleLocationSelect = (location: Location) => {
    setSelectedLocation(location);
    setCustomAddress('');
  };

  const handleCurrentLocation = () => {
    Alert.alert(
      'Use Current Location',
      'This would use GPS to get your current location.',
      [{ text: 'OK' }]
    );
  };

  const handleCustomLocation = () => {
    if (customAddress.trim()) {
      const customLocation: Location = {
        id: Date.now().toString(),
        name: 'Custom Location',
        address: customAddress,
        latitude: 51.5074, // Mock coordinates
        longitude: -0.1278,
        userId: 'current-user-id',
      };
      setSelectedLocation(customLocation);
    }
  };

  const handleContinue = () => {
    if (!selectedLocation) {
      Alert.alert('Error', 'Please select a location');
      return;
    }

    if (!isEmergencyService && !selectedDateTime) {
      Alert.alert('Error', 'Please select a date and time');
      return;
    }

    onComplete({
      location: selectedLocation,
      scheduledDateTime: selectedDateTime || undefined,
      notes: notes || undefined,
    });
  };

  const renderSavedLocations = () => {
    if (state.savedLocations.length === 0) return null;

    return (
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          Saved Locations
        </Text>
        <View style={styles.locationsList}>
          {state.savedLocations.map((location) => (
            <Card
              key={location.id}
              onPress={() => handleLocationSelect(location)}
              variant={selectedLocation?.id === location.id ? 'elevated' : 'outlined'}
              padding="md"
              style={[
                styles.locationCard,
                selectedLocation?.id === location.id && {
                  borderColor: colors.primary,
                  borderWidth: 2,
                },
              ]}
            >
              <View style={styles.locationContent}>
                <View style={styles.locationInfo}>
                  <Text style={[
                    styles.locationName,
                    { color: selectedLocation?.id === location.id ? colors.primary : colors.text }
                  ]}>
                    {location.name}
                  </Text>
                  <Text style={[styles.locationAddress, { color: colors.textSecondary }]}>
                    {location.address}
                  </Text>
                </View>
                {selectedLocation?.id === location.id && (
                  <Ionicons name="checkmark-circle" size={24} color={colors.primary} />
                )}
              </View>
            </Card>
          ))}
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={[styles.title, { color: colors.text }]}>
            {isEmergencyService ? 'Current Location' : 'Service Location & Time'}
          </Text>
          <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
            {isEmergencyService 
              ? 'Where do you need emergency assistance?'
              : 'Choose location and preferred time for your service'
            }
          </Text>
        </View>

        {/* Current Location Option */}
        <View style={styles.section}>
          <Button
            title="Use Current Location"
            onPress={handleCurrentLocation}
            variant="outline"
            icon={<Ionicons name="location" size={20} color={colors.primary} />}
            fullWidth
          />
        </View>

        {/* Saved Locations */}
        {renderSavedLocations()}

        {/* Custom Address */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Enter Custom Address
          </Text>
          <Input
            placeholder="Enter full address"
            value={customAddress}
            onChangeText={setCustomAddress}
            leftIcon={
              <Ionicons name="location-outline" size={20} color={colors.textSecondary} />
            }
            rightIcon={
              customAddress.trim() ? (
                <Button
                  title="Use"
                  onPress={handleCustomLocation}
                  size="sm"
                />
              ) : undefined
            }
          />
        </View>

        {/* Date & Time Selection (for scheduled service) */}
        {!isEmergencyService && (
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Preferred Date & Time
            </Text>
            <Input
              placeholder="Select date and time"
              value={selectedDateTime}
              onChangeText={setSelectedDateTime}
              leftIcon={
                <Ionicons name="calendar-outline" size={20} color={colors.textSecondary} />
              }
            />
          </View>
        )}

        {/* Additional Notes */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Additional Notes (Optional)
          </Text>
          <Input
            placeholder="Any special instructions or requirements..."
            value={notes}
            onChangeText={setNotes}
            multiline
            numberOfLines={3}
            leftIcon={
              <Ionicons name="document-text-outline" size={20} color={colors.textSecondary} />
            }
          />
        </View>

        {/* Selected Location Summary */}
        {selectedLocation && (
          <Card variant="elevated" padding="lg" style={styles.summaryCard}>
            <View style={styles.summaryHeader}>
              <Ionicons name="location" size={24} color={colors.primary} />
              <Text style={[styles.summaryTitle, { color: colors.text }]}>
                Selected Location
              </Text>
            </View>
            <Text style={[styles.summaryAddress, { color: colors.textSecondary }]}>
              {selectedLocation.address}
            </Text>
            {!isEmergencyService && selectedDateTime && (
              <View style={styles.summaryDateTime}>
                <Ionicons name="time" size={16} color={colors.primary} />
                <Text style={[styles.summaryDateTimeText, { color: colors.primary }]}>
                  {selectedDateTime}
                </Text>
              </View>
            )}
          </Card>
        )}
      </ScrollView>

      {/* Continue Button */}
      <View style={styles.footer}>
        <Button
          title="Continue"
          onPress={handleContinue}
          disabled={!selectedLocation}
          fullWidth
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: Spacing.lg,
  },
  header: {
    marginBottom: Spacing.xl,
  },
  title: {
    fontSize: Typography.fontSize['2xl'],
    fontWeight: Typography.fontWeight.bold,
    marginBottom: Spacing.sm,
  },
  subtitle: {
    fontSize: Typography.fontSize.base,
    lineHeight: Typography.lineHeight.lg,
  },
  section: {
    marginBottom: Spacing.xl,
  },
  sectionTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
    marginBottom: Spacing.base,
  },
  locationsList: {
    gap: Spacing.sm,
  },
  locationCard: {
    borderRadius: BorderRadius.md,
  },
  locationContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  locationInfo: {
    flex: 1,
  },
  locationName: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.medium,
    marginBottom: Spacing.xs / 2,
  },
  locationAddress: {
    fontSize: Typography.fontSize.sm,
  },
  summaryCard: {
    marginBottom: Spacing.xl,
  },
  summaryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.base,
    gap: Spacing.sm,
  },
  summaryTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
  },
  summaryAddress: {
    fontSize: Typography.fontSize.base,
    marginBottom: Spacing.sm,
  },
  summaryDateTime: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
  },
  summaryDateTimeText: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.medium,
  },
  footer: {
    padding: Spacing.lg,
    borderTopWidth: 1,
    borderTopColor: '#E9ECEF',
  },
});
