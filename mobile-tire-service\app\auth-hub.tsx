import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

import { Colors } from '@/constants/Colors';
import { Typography, Spacing, BorderRadius, Shadows } from '@/constants/Theme';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Card } from '@/components/ui';

export default function AuthHubScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  const handleLogin = () => {
    router.push('/login');
  };

  const handleRegister = () => {
    router.push('/register');
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.logoContainer}>
          <Image
            source={require('@/assets/images/adaptive-icon.png')} // Replace with actual logo
            style={styles.logo}
            resizeMode="contain"
          />
          <Text style={[styles.appName, { color: colors.primary }]}>
            Voltifi
          </Text>
          <Text style={[styles.tagline, { color: colors.textSecondary }]}>
            Mobile Tire Service & EV Charging
          </Text>
        </View>
      </View>

      {/* Main Content */}
      <View style={styles.content}>
        <View style={styles.welcomeSection}>
          <Text style={[styles.welcomeTitle, { color: colors.text }]}>
            Welcome Back!
          </Text>
          <Text style={[styles.welcomeSubtitle, { color: colors.textSecondary }]}>
            Choose an option to continue
          </Text>
        </View>

        {/* Auth Options */}
        <View style={styles.authOptions}>
          {/* Login Card */}
          <Card
            onPress={handleLogin}
            variant="elevated"
            padding="lg"
            style={[styles.authCard, { borderColor: colors.primary }]}
          >
            <View style={styles.authCardContent}>
              <View style={[styles.authIcon, { backgroundColor: colors.primary }]}>
                <Ionicons name="log-in-outline" size={32} color={colors.textLight} />
              </View>
              <View style={styles.authCardText}>
                <Text style={[styles.authCardTitle, { color: colors.text }]}>
                  Login
                </Text>
                <Text style={[styles.authCardSubtitle, { color: colors.textSecondary }]}>
                  Sign in to your existing account
                </Text>
              </View>
              <Ionicons name="chevron-forward" size={24} color={colors.textSecondary} />
            </View>
          </Card>

          {/* Register Card */}
          <Card
            onPress={handleRegister}
            variant="elevated"
            padding="lg"
            style={[styles.authCard, { borderColor: colors.secondary }]}
          >
            <View style={styles.authCardContent}>
              <View style={[styles.authIcon, { backgroundColor: colors.secondary }]}>
                <Ionicons name="person-add-outline" size={32} color={colors.textLight} />
              </View>
              <View style={styles.authCardText}>
                <Text style={[styles.authCardTitle, { color: colors.text }]}>
                  Register
                </Text>
                <Text style={[styles.authCardSubtitle, { color: colors.textSecondary }]}>
                  Create a new account to get started
                </Text>
              </View>
              <Ionicons name="chevron-forward" size={24} color={colors.textSecondary} />
            </View>
          </Card>
        </View>
      </View>

      {/* Footer */}
      <View style={styles.footer}>
        <Text style={[styles.footerText, { color: colors.textSecondary }]}>
          By continuing, you agree to our{' '}
          <Text style={[styles.linkText, { color: colors.primary }]}>
            Terms of Service
          </Text>
          {' '}and{' '}
          <Text style={[styles.linkText, { color: colors.primary }]}>
            Privacy Policy
          </Text>
        </Text>
        
        <View style={styles.supportSection}>
          <Text style={[styles.supportText, { color: colors.textSecondary }]}>
            Need help?{' '}
            <Text style={[styles.linkText, { color: colors.primary }]}>
              Contact Support
            </Text>
          </Text>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    alignItems: 'center',
    paddingTop: Spacing['2xl'],
    paddingBottom: Spacing.xl,
  },
  logoContainer: {
    alignItems: 'center',
  },
  logo: {
    width: 80,
    height: 80,
    marginBottom: Spacing.base,
  },
  appName: {
    fontSize: Typography.fontSize['3xl'],
    fontWeight: Typography.fontWeight.bold,
    marginBottom: Spacing.xs,
  },
  tagline: {
    fontSize: Typography.fontSize.sm,
    textAlign: 'center',
  },
  content: {
    flex: 1,
    paddingHorizontal: Spacing.xl,
  },
  welcomeSection: {
    alignItems: 'center',
    marginBottom: Spacing['3xl'],
  },
  welcomeTitle: {
    fontSize: Typography.fontSize['2xl'],
    fontWeight: Typography.fontWeight.bold,
    marginBottom: Spacing.xs,
  },
  welcomeSubtitle: {
    fontSize: Typography.fontSize.base,
    textAlign: 'center',
  },
  authOptions: {
    gap: Spacing.lg,
  },
  authCard: {
    borderWidth: 1,
    borderRadius: BorderRadius.lg,
    ...Shadows.md,
  },
  authCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  authIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.base,
  },
  authCardText: {
    flex: 1,
  },
  authCardTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
    marginBottom: Spacing.xs / 2,
  },
  authCardSubtitle: {
    fontSize: Typography.fontSize.sm,
    lineHeight: Typography.lineHeight.sm,
  },
  footer: {
    paddingHorizontal: Spacing.xl,
    paddingBottom: Spacing.xl,
    alignItems: 'center',
  },
  footerText: {
    fontSize: Typography.fontSize.xs,
    textAlign: 'center',
    lineHeight: Typography.lineHeight.sm,
    marginBottom: Spacing.base,
  },
  linkText: {
    fontWeight: Typography.fontWeight.medium,
  },
  supportSection: {
    marginTop: Spacing.sm,
  },
  supportText: {
    fontSize: Typography.fontSize.xs,
    textAlign: 'center',
  },
});
