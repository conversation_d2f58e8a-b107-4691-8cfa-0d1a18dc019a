/**
 * Voltifi App Color Scheme
 * Professional automotive/mobility color palette inspired by premium service platforms
 * Designed to convey trust, reliability, and modern convenience
 */

// Primary Brand Colors - Deep automotive blues with premium feel
const primaryDeepBlue = "#0B1426"; // Deep navy for headers and primary elements
const primaryBlue = "#1E3A8A"; // Rich blue for main actions and branding
const primaryBrightBlue = "#3B82F6"; // Bright blue for interactive elements
const primaryLightBlue = "#60A5FA"; // Light blue for accents and highlights

// Secondary Colors - Professional automotive palette
const secondaryCharcoal = "#374151"; // Professional charcoal for text
const secondarySteel = "#6B7280"; // Steel gray for secondary text
const secondaryOrange = "#F59E0B"; // Premium amber for warnings and highlights
const secondaryGreen = "#10B981"; // Success green for confirmations

// Neutral Colors - Clean and modern
const white = "#FFFFFF";
const neutralLightest = "#F9FAFB"; // Background tint
const neutralLight = "#F3F4F6"; // Card backgrounds
const neutralMedium = "#E5E7EB"; // Borders and dividers
const neutralDark = "#9CA3AF"; // Placeholder text
const neutralDarkest = "#111827"; // Primary text
const black = "#000000";

// Status Colors - Clear and accessible
const success = "#10B981"; // Green for success states
const warning = "#F59E0B"; // Amber for warnings
const error = "#EF4444"; // Red for errors
const info = "#3B82F6"; // Blue for information

// Automotive Accent Colors
const electricBlue = "#06B6D4"; // For EV charging features
const tireBlack = "#1F2937"; // For tire service elements
const premiumGold = "#D97706"; // For premium service indicators

// Gradient Colors - Modern and professional
const gradientStart = "#1E3A8A"; // Deep blue
const gradientEnd = "#3B82F6"; // Bright blue
const gradientAccent = "#06B6D4"; // Electric blue accent

export const Colors = {
  light: {
    // Primary Brand Colors
    primary: primaryBlue,
    primaryDark: primaryDeepBlue,
    primaryBright: primaryBrightBlue,
    primaryLight: primaryLightBlue,

    // Secondary Colors
    secondary: secondaryOrange,
    accent: secondaryGreen,
    charcoal: secondaryCharcoal,
    steel: secondarySteel,

    // Backgrounds
    background: neutralLightest,
    surface: white,
    card: white,
    cardElevated: neutralLight,
    overlay: "rgba(11, 20, 38, 0.6)",
    overlayLight: "rgba(11, 20, 38, 0.3)",

    // Text Colors
    text: neutralDarkest,
    textSecondary: secondaryCharcoal,
    textTertiary: secondarySteel,
    textLight: white,
    textMuted: neutralDark,

    // Borders & Dividers
    border: neutralMedium,
    borderLight: neutralLight,
    borderSubtle: "#F3F4F6",

    // Status Colors
    success: success,
    warning: warning,
    error: error,
    info: info,

    // Navigation
    tint: primaryBlue,
    icon: secondarySteel,
    iconActive: primaryBlue,
    tabIconDefault: neutralDark,
    tabIconSelected: primaryBlue,

    // Gradients
    gradientStart: gradientStart,
    gradientEnd: gradientEnd,
    gradientAccent: gradientAccent,

    // Automotive Specific
    electric: electricBlue,
    tire: tireBlack,
    premium: premiumGold,

    // Interactive States
    disabled: neutralDark,
    placeholder: neutralDark,
    shadow: "rgba(11, 20, 38, 0.08)",
    shadowMedium: "rgba(11, 20, 38, 0.12)",
    shadowStrong: "rgba(11, 20, 38, 0.16)",
  },
  dark: {
    // Primary Brand Colors
    primary: primaryBrightBlue,
    primaryDark: primaryDeepBlue,
    primaryBright: primaryLightBlue,
    primaryLight: primaryBrightBlue,

    // Secondary Colors
    secondary: secondaryOrange,
    accent: secondaryGreen,
    charcoal: "#D1D5DB",
    steel: "#9CA3AF",

    // Backgrounds
    background: "#0F172A",
    surface: "#1E293B",
    card: "#334155",
    cardElevated: "#475569",
    overlay: "rgba(15, 23, 42, 0.8)",
    overlayLight: "rgba(15, 23, 42, 0.6)",

    // Text Colors
    text: "#F8FAFC",
    textSecondary: "#E2E8F0",
    textTertiary: "#CBD5E1",
    textLight: white,
    textMuted: "#94A3B8",

    // Borders & Dividers
    border: "#475569",
    borderLight: "#64748B",
    borderSubtle: "#334155",

    // Status Colors
    success: success,
    warning: warning,
    error: error,
    info: info,

    // Navigation
    tint: primaryBrightBlue,
    icon: "#94A3B8",
    iconActive: primaryBrightBlue,
    tabIconDefault: "#64748B",
    tabIconSelected: primaryBrightBlue,

    // Gradients
    gradientStart: gradientStart,
    gradientEnd: gradientEnd,
    gradientAccent: gradientAccent,

    // Automotive Specific
    electric: electricBlue,
    tire: "#F1F5F9",
    premium: premiumGold,

    // Interactive States
    disabled: "#64748B",
    placeholder: "#94A3B8",
    shadow: "rgba(0, 0, 0, 0.4)",
    shadowMedium: "rgba(0, 0, 0, 0.5)",
    shadowStrong: "rgba(0, 0, 0, 0.6)",
  },
};
