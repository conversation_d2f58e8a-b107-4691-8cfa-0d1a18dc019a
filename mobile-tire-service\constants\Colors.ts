/**
 * Voltifi App Color Scheme
 * Professional color palette for mobile tire service and EV charging platform
 */

// Primary Colors
const primaryBlue = '#007AFF';
const primaryNavy = '#1A1A2E';

// Secondary Colors
const secondaryOrange = '#FF6B35';
const secondaryGreen = '#34C759';

// Neutral Colors
const white = '#FFFFFF';
const lightGray = '#F8F9FA';
const mediumGray = '#6C757D';
const darkGray = '#343A40';
const black = '#000000';

// Status Colors
const success = '#28A745';
const warning = '#FFC107';
const error = '#DC3545';
const info = '#17A2B8';

// Gradient Colors
const gradientStart = '#007AFF';
const gradientEnd = '#5856D6';

export const Colors = {
  light: {
    // Primary
    primary: primaryBlue,
    primaryDark: primaryNavy,
    secondary: secondaryOrange,
    accent: secondaryGreen,

    // Backgrounds
    background: white,
    surface: white,
    card: white,
    overlay: 'rgba(0, 0, 0, 0.5)',

    // Text
    text: darkGray,
    textSecondary: mediumGray,
    textLight: white,

    // Borders
    border: '#E9ECEF',
    borderLight: '#F1F3F4',

    // Status
    success: success,
    warning: warning,
    error: error,
    info: info,

    // Navigation
    tint: primaryBlue,
    icon: mediumGray,
    tabIconDefault: mediumGray,
    tabIconSelected: primaryBlue,

    // Gradients
    gradientStart: gradientStart,
    gradientEnd: gradientEnd,

    // Special
    disabled: '#ADB5BD',
    placeholder: '#6C757D',
    shadow: 'rgba(0, 0, 0, 0.1)',
  },
  dark: {
    // Primary
    primary: primaryBlue,
    primaryDark: primaryNavy,
    secondary: secondaryOrange,
    accent: secondaryGreen,

    // Backgrounds
    background: '#121212',
    surface: '#1E1E1E',
    card: '#2C2C2E',
    overlay: 'rgba(0, 0, 0, 0.7)',

    // Text
    text: '#FFFFFF',
    textSecondary: '#AEAEB2',
    textLight: white,

    // Borders
    border: '#38383A',
    borderLight: '#48484A',

    // Status
    success: success,
    warning: warning,
    error: error,
    info: info,

    // Navigation
    tint: primaryBlue,
    icon: '#AEAEB2',
    tabIconDefault: '#AEAEB2',
    tabIconSelected: primaryBlue,

    // Gradients
    gradientStart: gradientStart,
    gradientEnd: gradientEnd,

    // Special
    disabled: '#48484A',
    placeholder: '#AEAEB2',
    shadow: 'rgba(0, 0, 0, 0.3)',
  },
};
