import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { Colors } from '@/constants/Colors';
import { Typography, Spacing, BorderRadius } from '@/constants/Theme';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Card, Button, Input } from '@/components/ui';
import { BookingFlow, Vehicle, DVLAVehicleData } from '@/types';
import { useApp } from '@/contexts';

interface VehicleStepProps {
  onComplete: (data: Partial<BookingFlow>) => void;
  initialData: BookingFlow;
}

export default function VehicleStep({ onComplete, initialData }: VehicleStepProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const { state } = useApp();

  const [registrationNumber, setRegistrationNumber] = useState(
    initialData.vehicle?.registrationNumber || ''
  );
  const [vehicleData, setVehicleData] = useState<DVLAVehicleData | null>(
    initialData.vehicle ? {
      registrationNumber: initialData.vehicle.registrationNumber,
      make: initialData.vehicle.make,
      model: initialData.vehicle.model,
      year: initialData.vehicle.year,
      tireSize: initialData.vehicle.tireSize,
      loadRating: initialData.vehicle.loadRating,
      speedRating: initialData.vehicle.speedRating,
    } : null
  );
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const handleRegistrationLookup = async () => {
    if (!registrationNumber.trim()) {
      setError('Please enter a registration number');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      // Simulate DVLA API call
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Mock vehicle data - in real app, this would come from DVLA API
      const mockVehicleData: DVLAVehicleData = {
        registrationNumber: registrationNumber.toUpperCase(),
        make: 'BMW',
        model: '3 Series',
        year: 2020,
        engineSize: '2.0L',
        fuelType: 'Petrol',
        color: 'Black',
        tireSize: '225/45R17',
        loadRating: '94',
        speedRating: 'W',
      };

      setVehicleData(mockVehicleData);
    } catch (error) {
      setError('Unable to find vehicle details. Please check the registration number.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleContinue = () => {
    if (!vehicleData) {
      setError('Please lookup vehicle details first');
      return;
    }

    const vehicle: Vehicle = {
      id: Date.now().toString(),
      registrationNumber: vehicleData.registrationNumber,
      make: vehicleData.make,
      model: vehicleData.model,
      year: vehicleData.year,
      color: vehicleData.color,
      tireSize: vehicleData.tireSize,
      loadRating: vehicleData.loadRating,
      speedRating: vehicleData.speedRating,
      userId: 'current-user-id', // This would come from auth context
    };

    onComplete({ vehicle });
  };

  const handleSelectSavedVehicle = (vehicle: Vehicle) => {
    setRegistrationNumber(vehicle.registrationNumber);
    setVehicleData({
      registrationNumber: vehicle.registrationNumber,
      make: vehicle.make,
      model: vehicle.model,
      year: vehicle.year,
      color: vehicle.color,
      tireSize: vehicle.tireSize,
      loadRating: vehicle.loadRating,
      speedRating: vehicle.speedRating,
    });
  };

  const renderSavedVehicles = () => {
    if (state.userVehicles.length === 0) return null;

    return (
      <View style={styles.savedVehiclesSection}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          Your Vehicles
        </Text>
        <View style={styles.savedVehiclesList}>
          {state.userVehicles.map((vehicle) => (
            <Card
              key={vehicle.id}
              onPress={() => handleSelectSavedVehicle(vehicle)}
              variant="outlined"
              padding="md"
              style={styles.savedVehicleCard}
            >
              <View style={styles.savedVehicleContent}>
                <View style={styles.savedVehicleInfo}>
                  <Text style={[styles.savedVehicleTitle, { color: colors.text }]}>
                    {vehicle.make} {vehicle.model}
                  </Text>
                  <Text style={[styles.savedVehicleSubtitle, { color: colors.textSecondary }]}>
                    {vehicle.registrationNumber} • {vehicle.year}
                  </Text>
                </View>
                <Ionicons name="chevron-forward" size={20} color={colors.textSecondary} />
              </View>
            </Card>
          ))}
        </View>
      </View>
    );
  };

  const renderVehicleDetails = () => {
    if (!vehicleData) return null;

    return (
      <Card variant="elevated" padding="lg" style={styles.vehicleDetailsCard}>
        <View style={styles.vehicleDetailsHeader}>
          <Ionicons name="car" size={32} color={colors.primary} />
          <Text style={[styles.vehicleDetailsTitle, { color: colors.text }]}>
            Vehicle Details
          </Text>
        </View>

        <View style={styles.vehicleDetailsContent}>
          <View style={styles.detailRow}>
            <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
              Registration:
            </Text>
            <Text style={[styles.detailValue, { color: colors.text }]}>
              {vehicleData.registrationNumber}
            </Text>
          </View>

          <View style={styles.detailRow}>
            <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
              Make & Model:
            </Text>
            <Text style={[styles.detailValue, { color: colors.text }]}>
              {vehicleData.make} {vehicleData.model}
            </Text>
          </View>

          <View style={styles.detailRow}>
            <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
              Year:
            </Text>
            <Text style={[styles.detailValue, { color: colors.text }]}>
              {vehicleData.year}
            </Text>
          </View>

          {vehicleData.color && (
            <View style={styles.detailRow}>
              <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
                Color:
              </Text>
              <Text style={[styles.detailValue, { color: colors.text }]}>
                {vehicleData.color}
              </Text>
            </View>
          )}

          <View style={styles.tireDetailsSection}>
            <Text style={[styles.tireDetailsTitle, { color: colors.text }]}>
              Tire Specifications
            </Text>
            
            <View style={styles.detailRow}>
              <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
                Tire Size:
              </Text>
              <Text style={[styles.detailValue, { color: colors.text }]}>
                {vehicleData.tireSize}
              </Text>
            </View>

            <View style={styles.detailRow}>
              <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
                Load Rating:
              </Text>
              <Text style={[styles.detailValue, { color: colors.text }]}>
                {vehicleData.loadRating}
              </Text>
            </View>

            <View style={styles.detailRow}>
              <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
                Speed Rating:
              </Text>
              <Text style={[styles.detailValue, { color: colors.text }]}>
                {vehicleData.speedRating}
              </Text>
            </View>
          </View>
        </View>
      </Card>
    );
  };

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={[styles.title, { color: colors.text }]}>
            Vehicle Information
          </Text>
          <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
            Enter your vehicle registration to get tire specifications
          </Text>
        </View>

        {/* Saved Vehicles */}
        {renderSavedVehicles()}

        {/* Registration Input */}
        <View style={styles.inputSection}>
          <Input
            label="Vehicle Registration Number"
            placeholder="e.g., AB12 CDE"
            value={registrationNumber}
            onChangeText={setRegistrationNumber}
            error={error}
            autoCapitalize="characters"
            leftIcon={
              <Ionicons name="car-outline" size={20} color={colors.textSecondary} />
            }
            rightIcon={
              <Button
                title="Lookup"
                onPress={handleRegistrationLookup}
                loading={isLoading}
                disabled={isLoading || !registrationNumber.trim()}
                size="sm"
              />
            }
            required
          />
        </View>

        {/* Vehicle Details */}
        {renderVehicleDetails()}
      </ScrollView>

      {/* Continue Button */}
      <View style={styles.footer}>
        <Button
          title="Continue"
          onPress={handleContinue}
          disabled={!vehicleData}
          fullWidth
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: Spacing.lg,
  },
  header: {
    marginBottom: Spacing.xl,
  },
  title: {
    fontSize: Typography.fontSize['2xl'],
    fontWeight: Typography.fontWeight.bold,
    marginBottom: Spacing.sm,
  },
  subtitle: {
    fontSize: Typography.fontSize.base,
    lineHeight: Typography.lineHeight.lg,
  },
  savedVehiclesSection: {
    marginBottom: Spacing.xl,
  },
  sectionTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
    marginBottom: Spacing.base,
  },
  savedVehiclesList: {
    gap: Spacing.sm,
  },
  savedVehicleCard: {
    borderRadius: BorderRadius.md,
  },
  savedVehicleContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  savedVehicleInfo: {
    flex: 1,
  },
  savedVehicleTitle: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.medium,
    marginBottom: Spacing.xs / 2,
  },
  savedVehicleSubtitle: {
    fontSize: Typography.fontSize.sm,
  },
  inputSection: {
    marginBottom: Spacing.xl,
  },
  vehicleDetailsCard: {
    marginBottom: Spacing.xl,
  },
  vehicleDetailsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.lg,
    gap: Spacing.base,
  },
  vehicleDetailsTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
  },
  vehicleDetailsContent: {
    gap: Spacing.base,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  detailLabel: {
    fontSize: Typography.fontSize.sm,
    flex: 1,
  },
  detailValue: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.medium,
    flex: 1,
    textAlign: 'right',
  },
  tireDetailsSection: {
    marginTop: Spacing.base,
    paddingTop: Spacing.base,
    borderTopWidth: 1,
    borderTopColor: '#E9ECEF',
  },
  tireDetailsTitle: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.semibold,
    marginBottom: Spacing.base,
  },
  footer: {
    padding: Spacing.lg,
    borderTopWidth: 1,
    borderTopColor: '#E9ECEF',
  },
});
