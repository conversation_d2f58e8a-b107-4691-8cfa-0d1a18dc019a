import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { Colors } from '@/constants/Colors';
import { Typography, Spacing, BorderRadius } from '@/constants/Theme';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Card, Button } from '@/components/ui';
import { BookingFlow, Booking } from '@/types';
import { useApp, useAuth } from '@/contexts';

interface ConfirmationStepProps {
  onComplete: () => void;
  bookingData: BookingFlow;
}

export default function ConfirmationStep({ onComplete, bookingData }: ConfirmationStepProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const { addBooking } = useApp();
  const { state: authState } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Calculate estimated cost
  const baseServiceCost = bookingData.serviceType === 'emergency' ? 120 : 80;
  const brandMultiplier = bookingData.selectedBrands?.[0]?.category === 'premium' ? 1.5 : 
                         bookingData.selectedBrands?.[0]?.category === 'mid-range' ? 1.2 : 1.0;
  const estimatedCost = Math.round(baseServiceCost * brandMultiplier);

  const handleConfirmBooking = async () => {
    setIsSubmitting(true);

    try {
      // Create booking object
      const booking: Booking = {
        id: Date.now().toString(),
        userId: authState.user?.id || '',
        serviceProviderId: bookingData.serviceProvider?.id || '',
        vehicleId: bookingData.vehicle?.id || '',
        locationId: bookingData.location?.id || '',
        serviceType: bookingData.serviceType || 'scheduled',
        services: [{
          serviceId: '1',
          tireBrandId: bookingData.selectedBrands?.[0]?.id || '',
          quantity: 1,
          unitPrice: estimatedCost,
          totalPrice: estimatedCost,
        }],
        scheduledDateTime: bookingData.scheduledDateTime,
        status: 'pending',
        totalAmount: estimatedCost,
        notes: bookingData.notes,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        // Populated fields
        user: authState.user || undefined,
        serviceProvider: bookingData.serviceProvider,
        vehicle: bookingData.vehicle,
        location: bookingData.location,
      };

      // Add booking to app state
      await addBooking(booking);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      onComplete();
    } catch (error) {
      console.error('Error creating booking:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderSummarySection = (title: string, icon: string, children: React.ReactNode) => (
    <Card variant="outlined" padding="lg" style={styles.summaryCard}>
      <View style={styles.summaryHeader}>
        <Ionicons name={icon as any} size={24} color={colors.primary} />
        <Text style={[styles.summaryTitle, { color: colors.text }]}>
          {title}
        </Text>
      </View>
      {children}
    </Card>
  );

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={[styles.title, { color: colors.text }]}>
            Confirm Your Booking
          </Text>
          <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
            Please review your booking details before confirming
          </Text>
        </View>

        {/* Service Type */}
        {renderSummarySection(
          'Service Type',
          bookingData.serviceType === 'emergency' ? 'flash' : 'calendar',
          <View>
            <Text style={[styles.summaryValue, { color: colors.text }]}>
              {bookingData.serviceType === 'emergency' ? 'Emergency Tire Assistance' : 'Scheduled Tire Service'}
            </Text>
            <Text style={[styles.summarySubvalue, { color: colors.textSecondary }]}>
              {bookingData.serviceType === 'emergency' ? 'ASAP within 1-2 hours' : 'At your preferred time'}
            </Text>
          </View>
        )}

        {/* Vehicle */}
        {bookingData.vehicle && renderSummarySection(
          'Vehicle',
          'car',
          <View>
            <Text style={[styles.summaryValue, { color: colors.text }]}>
              {bookingData.vehicle.make} {bookingData.vehicle.model}
            </Text>
            <Text style={[styles.summarySubvalue, { color: colors.textSecondary }]}>
              {bookingData.vehicle.registrationNumber} • {bookingData.vehicle.year}
            </Text>
            <Text style={[styles.summarySubvalue, { color: colors.textSecondary }]}>
              Tire Size: {bookingData.vehicle.tireSize}
            </Text>
          </View>
        )}

        {/* Tire Brand */}
        {bookingData.selectedBrands && bookingData.selectedBrands.length > 0 && renderSummarySection(
          'Tire Brand',
          'settings',
          <View>
            {bookingData.selectedBrands.map((brand, index) => (
              <View key={brand.id}>
                <Text style={[styles.summaryValue, { color: colors.text }]}>
                  {brand.name}
                </Text>
                <Text style={[styles.summarySubvalue, { color: colors.textSecondary }]}>
                  {brand.category.charAt(0).toUpperCase() + brand.category.slice(1)} Category
                </Text>
              </View>
            ))}
          </View>
        )}

        {/* Service Provider */}
        {bookingData.serviceProvider && renderSummarySection(
          'Service Provider',
          'business',
          <View>
            <Text style={[styles.summaryValue, { color: colors.text }]}>
              {bookingData.serviceProvider.businessName}
            </Text>
            <Text style={[styles.summarySubvalue, { color: colors.textSecondary }]}>
              {bookingData.serviceProvider.contactPerson}
            </Text>
            <View style={styles.ratingContainer}>
              <Ionicons name="star" size={16} color={colors.warning} />
              <Text style={[styles.ratingText, { color: colors.textSecondary }]}>
                {bookingData.serviceProvider.rating.toFixed(1)} ({bookingData.serviceProvider.reviewCount} reviews)
              </Text>
            </View>
          </View>
        )}

        {/* Location */}
        {bookingData.location && renderSummarySection(
          'Service Location',
          'location',
          <View>
            <Text style={[styles.summaryValue, { color: colors.text }]}>
              {bookingData.location.name}
            </Text>
            <Text style={[styles.summarySubvalue, { color: colors.textSecondary }]}>
              {bookingData.location.address}
            </Text>
          </View>
        )}

        {/* Date & Time */}
        {bookingData.scheduledDateTime && renderSummarySection(
          'Scheduled Time',
          'time',
          <Text style={[styles.summaryValue, { color: colors.text }]}>
            {bookingData.scheduledDateTime}
          </Text>
        )}

        {/* Notes */}
        {bookingData.notes && renderSummarySection(
          'Additional Notes',
          'document-text',
          <Text style={[styles.summaryValue, { color: colors.text }]}>
            {bookingData.notes}
          </Text>
        )}

        {/* Cost Summary */}
        <Card variant="elevated" padding="lg" style={[styles.summaryCard, styles.costCard]}>
          <View style={styles.summaryHeader}>
            <Ionicons name="cash" size={24} color={colors.primary} />
            <Text style={[styles.summaryTitle, { color: colors.text }]}>
              Cost Summary
            </Text>
          </View>
          
          <View style={styles.costRow}>
            <Text style={[styles.costLabel, { color: colors.textSecondary }]}>
              Service Fee:
            </Text>
            <Text style={[styles.costValue, { color: colors.text }]}>
              £{baseServiceCost.toFixed(2)}
            </Text>
          </View>
          
          {brandMultiplier !== 1.0 && (
            <View style={styles.costRow}>
              <Text style={[styles.costLabel, { color: colors.textSecondary }]}>
                Brand Premium:
              </Text>
              <Text style={[styles.costValue, { color: colors.text }]}>
                £{(estimatedCost - baseServiceCost).toFixed(2)}
              </Text>
            </View>
          )}
          
          <View style={[styles.costRow, styles.totalRow]}>
            <Text style={[styles.totalLabel, { color: colors.text }]}>
              Total Estimated Cost:
            </Text>
            <Text style={[styles.totalValue, { color: colors.primary }]}>
              £{estimatedCost.toFixed(2)}
            </Text>
          </View>
        </Card>
      </ScrollView>

      {/* Confirm Button */}
      <View style={styles.footer}>
        <Button
          title={isSubmitting ? 'Confirming...' : 'Confirm Booking'}
          onPress={handleConfirmBooking}
          loading={isSubmitting}
          disabled={isSubmitting}
          fullWidth
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: Spacing.lg,
  },
  header: {
    marginBottom: Spacing.xl,
  },
  title: {
    fontSize: Typography.fontSize['2xl'],
    fontWeight: Typography.fontWeight.bold,
    marginBottom: Spacing.sm,
  },
  subtitle: {
    fontSize: Typography.fontSize.base,
    lineHeight: Typography.lineHeight.lg,
  },
  summaryCard: {
    marginBottom: Spacing.base,
    borderRadius: BorderRadius.lg,
  },
  summaryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.base,
    gap: Spacing.sm,
  },
  summaryTitle: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.semibold,
  },
  summaryValue: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.medium,
    marginBottom: Spacing.xs / 2,
  },
  summarySubvalue: {
    fontSize: Typography.fontSize.sm,
    marginBottom: Spacing.xs / 2,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.xs,
    marginTop: Spacing.xs,
  },
  ratingText: {
    fontSize: Typography.fontSize.sm,
  },
  costCard: {
    marginTop: Spacing.base,
  },
  costRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  costLabel: {
    fontSize: Typography.fontSize.sm,
  },
  costValue: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.medium,
  },
  totalRow: {
    paddingTop: Spacing.sm,
    borderTopWidth: 1,
    borderTopColor: '#E9ECEF',
    marginTop: Spacing.sm,
  },
  totalLabel: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.semibold,
  },
  totalValue: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.bold,
  },
  footer: {
    padding: Spacing.lg,
    borderTopWidth: 1,
    borderTopColor: '#E9ECEF',
  },
});
