import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { Colors } from '@/constants/Colors';
import { Typography, Spacing, BorderRadius } from '@/constants/Theme';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Card, Button } from '@/components/ui';
import { BookingFlow, TireBrand } from '@/types';
import { useApp } from '@/contexts';

interface TireBrandStepProps {
  onComplete: (data: Partial<BookingFlow>) => void;
  initialData: BookingFlow;
}

type BrandCategory = 'premium' | 'mid-range' | 'budget';

interface CategoryInfo {
  key: BrandCategory;
  title: string;
  description: string;
  priceRange: string;
  icon: string;
  color: string;
}

const categories: CategoryInfo[] = [
  {
    key: 'premium',
    title: 'Premium',
    description: 'Top-quality tires with advanced technology',
    priceRange: '£150 - £300 per tire',
    icon: 'diamond',
    color: '#007AFF',
  },
  {
    key: 'mid-range',
    title: 'Mid-Range',
    description: 'Great balance of quality and value',
    priceRange: '£80 - £150 per tire',
    icon: 'star',
    color: '#FF9500',
  },
  {
    key: 'budget',
    title: 'Budget Friendly',
    description: 'Affordable options without compromising safety',
    priceRange: '£40 - £80 per tire',
    icon: 'wallet',
    color: '#34C759',
  },
];

export default function TireBrandStep({ onComplete, initialData }: TireBrandStepProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const { state } = useApp();

  const [selectedCategory, setSelectedCategory] = useState<BrandCategory | null>(null);
  const [selectedBrands, setSelectedBrands] = useState<TireBrand[]>(
    initialData.selectedBrands || []
  );

  const filteredBrands = state.tireBrands.filter(brand => 
    selectedCategory ? brand.category === selectedCategory : true
  );

  const handleCategorySelect = (category: BrandCategory) => {
    setSelectedCategory(category);
    setSelectedBrands([]); // Clear selected brands when category changes
  };

  const handleBrandToggle = (brand: TireBrand) => {
    setSelectedBrands(prev => {
      const isSelected = prev.some(b => b.id === brand.id);
      if (isSelected) {
        return prev.filter(b => b.id !== brand.id);
      } else {
        return [...prev, brand];
      }
    });
  };

  const handleContinue = () => {
    if (selectedBrands.length > 0) {
      onComplete({ selectedBrands });
    }
  };

  const renderCategoryCard = (category: CategoryInfo) => {
    const isSelected = selectedCategory === category.key;
    
    return (
      <Card
        key={category.key}
        onPress={() => handleCategorySelect(category.key)}
        variant={isSelected ? 'elevated' : 'outlined'}
        padding="lg"
        style={[
          styles.categoryCard,
          isSelected && {
            borderColor: category.color,
            borderWidth: 2,
          },
        ]}
      >
        <View style={styles.categoryHeader}>
          <View style={[
            styles.categoryIcon,
            { backgroundColor: isSelected ? category.color : colors.border }
          ]}>
            <Ionicons
              name={category.icon as any}
              size={24}
              color={isSelected ? colors.textLight : colors.textSecondary}
            />
          </View>
          
          <View style={styles.categoryInfo}>
            <Text style={[
              styles.categoryTitle,
              { color: isSelected ? category.color : colors.text }
            ]}>
              {category.title}
            </Text>
            <Text style={[styles.categoryPrice, { color: colors.textSecondary }]}>
              {category.priceRange}
            </Text>
          </View>
          
          {isSelected && (
            <Ionicons name="checkmark-circle" size={24} color={category.color} />
          )}
        </View>

        <Text style={[styles.categoryDescription, { color: colors.textSecondary }]}>
          {category.description}
        </Text>
      </Card>
    );
  };

  const renderBrandCard = (brand: TireBrand) => {
    const isSelected = selectedBrands.some(b => b.id === brand.id);
    const categoryInfo = categories.find(c => c.key === brand.category);
    
    return (
      <Card
        key={brand.id}
        onPress={() => handleBrandToggle(brand)}
        variant={isSelected ? 'elevated' : 'outlined'}
        padding="md"
        style={[
          styles.brandCard,
          isSelected && {
            borderColor: categoryInfo?.color || colors.primary,
            borderWidth: 2,
          },
        ]}
      >
        <View style={styles.brandContent}>
          <View style={styles.brandInfo}>
            <Text style={[
              styles.brandName,
              { color: isSelected ? categoryInfo?.color || colors.primary : colors.text }
            ]}>
              {brand.name}
            </Text>
            {brand.description && (
              <Text style={[styles.brandDescription, { color: colors.textSecondary }]}>
                {brand.description}
              </Text>
            )}
          </View>
          
          <View style={styles.brandActions}>
            <View style={[
              styles.categoryBadge,
              { backgroundColor: (categoryInfo?.color || colors.primary) + '20' }
            ]}>
              <Text style={[
                styles.categoryBadgeText,
                { color: categoryInfo?.color || colors.primary }
              ]}>
                {brand.category.charAt(0).toUpperCase() + brand.category.slice(1)}
              </Text>
            </View>
            
            {isSelected ? (
              <Ionicons 
                name="checkmark-circle" 
                size={24} 
                color={categoryInfo?.color || colors.primary} 
              />
            ) : (
              <Ionicons 
                name="radio-button-off" 
                size={24} 
                color={colors.border} 
              />
            )}
          </View>
        </View>
      </Card>
    );
  };

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={[styles.title, { color: colors.text }]}>
            Choose Tire Brand
          </Text>
          <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
            Select your preferred tire category and brands
          </Text>
        </View>

        {/* Category Selection */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Select Category
          </Text>
          <View style={styles.categoriesContainer}>
            {categories.map(renderCategoryCard)}
          </View>
        </View>

        {/* Brand Selection */}
        {selectedCategory && (
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Available Brands
            </Text>
            <View style={styles.brandsContainer}>
              {filteredBrands.map(renderBrandCard)}
            </View>
          </View>
        )}

        {/* Selected Brands Summary */}
        {selectedBrands.length > 0 && (
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Selected Brands ({selectedBrands.length})
            </Text>
            <View style={styles.selectedBrandsContainer}>
              {selectedBrands.map((brand) => (
                <View key={brand.id} style={[styles.selectedBrandChip, { backgroundColor: colors.primary + '20' }]}>
                  <Text style={[styles.selectedBrandText, { color: colors.primary }]}>
                    {brand.name}
                  </Text>
                  <TouchableOpacity onPress={() => handleBrandToggle(brand)}>
                    <Ionicons name="close" size={16} color={colors.primary} />
                  </TouchableOpacity>
                </View>
              ))}
            </View>
          </View>
        )}
      </ScrollView>

      {/* Continue Button */}
      <View style={styles.footer}>
        <Button
          title={`Continue with ${selectedBrands.length} brand${selectedBrands.length !== 1 ? 's' : ''}`}
          onPress={handleContinue}
          disabled={selectedBrands.length === 0}
          fullWidth
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: Spacing.lg,
  },
  header: {
    marginBottom: Spacing.xl,
  },
  title: {
    fontSize: Typography.fontSize['2xl'],
    fontWeight: Typography.fontWeight.bold,
    marginBottom: Spacing.sm,
  },
  subtitle: {
    fontSize: Typography.fontSize.base,
    lineHeight: Typography.lineHeight.lg,
  },
  section: {
    marginBottom: Spacing.xl,
  },
  sectionTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
    marginBottom: Spacing.base,
  },
  categoriesContainer: {
    gap: Spacing.base,
  },
  categoryCard: {
    borderRadius: BorderRadius.lg,
  },
  categoryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.base,
  },
  categoryIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.base,
  },
  categoryInfo: {
    flex: 1,
  },
  categoryTitle: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.semibold,
    marginBottom: Spacing.xs / 2,
  },
  categoryPrice: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.medium,
  },
  categoryDescription: {
    fontSize: Typography.fontSize.sm,
    lineHeight: Typography.lineHeight.sm,
  },
  brandsContainer: {
    gap: Spacing.sm,
  },
  brandCard: {
    borderRadius: BorderRadius.md,
  },
  brandContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  brandInfo: {
    flex: 1,
  },
  brandName: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.medium,
    marginBottom: Spacing.xs / 2,
  },
  brandDescription: {
    fontSize: Typography.fontSize.sm,
  },
  brandActions: {
    alignItems: 'center',
    gap: Spacing.sm,
  },
  categoryBadge: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs / 2,
    borderRadius: BorderRadius.sm,
  },
  categoryBadgeText: {
    fontSize: Typography.fontSize.xs,
    fontWeight: Typography.fontWeight.medium,
  },
  selectedBrandsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.sm,
  },
  selectedBrandChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Spacing.base,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.full,
    gap: Spacing.sm,
  },
  selectedBrandText: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.medium,
  },
  footer: {
    padding: Spacing.lg,
    borderTopWidth: 1,
    borderTopColor: '#E9ECEF',
  },
});
