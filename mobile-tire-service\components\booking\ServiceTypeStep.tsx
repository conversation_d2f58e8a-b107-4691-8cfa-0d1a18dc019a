import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { Colors } from '@/constants/Colors';
import { Typography, Spacing, BorderRadius } from '@/constants/Theme';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Card, Button } from '@/components/ui';
import { BookingFlow } from '@/types';

interface ServiceTypeStepProps {
  onComplete: (data: Partial<BookingFlow>) => void;
  initialData: BookingFlow;
}

type ServiceType = 'emergency' | 'scheduled';

interface ServiceOption {
  type: ServiceType;
  title: string;
  subtitle: string;
  description: string;
  icon: string;
  timeframe: string;
  features: string[];
}

const serviceOptions: ServiceOption[] = [
  {
    type: 'emergency',
    title: 'Emergency Tire Assistance',
    subtitle: 'ASAP Service',
    description: 'Get immediate tire assistance when you need it most',
    icon: 'flash',
    timeframe: 'Within 1-2 hours',
    features: [
      '24/7 availability',
      'Priority response',
      'Mobile service unit',
      'Emergency roadside assistance',
    ],
  },
  {
    type: 'scheduled',
    title: 'Book a Tire Service Slot',
    subtitle: 'Scheduled Service',
    description: 'Plan your tire service at a convenient time',
    icon: 'calendar',
    timeframe: 'Choose your preferred time',
    features: [
      'Flexible scheduling',
      'Better pricing',
      'Comprehensive service',
      'Quality guarantee',
    ],
  },
];

export default function ServiceTypeStep({ onComplete, initialData }: ServiceTypeStepProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const [selectedType, setSelectedType] = useState<ServiceType | null>(
    initialData.serviceType || null
  );

  const handleServiceSelect = (type: ServiceType) => {
    setSelectedType(type);
  };

  const handleContinue = () => {
    if (selectedType) {
      onComplete({ serviceType: selectedType });
    }
  };

  const renderServiceOption = (option: ServiceOption) => {
    const isSelected = selectedType === option.type;
    
    return (
      <Card
        key={option.type}
        onPress={() => handleServiceSelect(option.type)}
        variant={isSelected ? 'elevated' : 'outlined'}
        padding="lg"
        style={[
          styles.serviceCard,
          isSelected && {
            borderColor: colors.primary,
            borderWidth: 2,
          },
        ]}
      >
        <View style={styles.serviceHeader}>
          <View style={[
            styles.serviceIcon,
            { backgroundColor: isSelected ? colors.primary : colors.border }
          ]}>
            <Ionicons
              name={option.icon as any}
              size={32}
              color={isSelected ? colors.textLight : colors.textSecondary}
            />
          </View>
          
          <View style={styles.serviceInfo}>
            <Text style={[
              styles.serviceTitle,
              { color: isSelected ? colors.primary : colors.text }
            ]}>
              {option.title}
            </Text>
            <Text style={[styles.serviceSubtitle, { color: colors.textSecondary }]}>
              {option.subtitle}
            </Text>
          </View>
          
          {isSelected && (
            <Ionicons name="checkmark-circle" size={24} color={colors.primary} />
          )}
        </View>

        <Text style={[styles.serviceDescription, { color: colors.textSecondary }]}>
          {option.description}
        </Text>

        <View style={styles.timeframeContainer}>
          <Ionicons name="time-outline" size={16} color={colors.primary} />
          <Text style={[styles.timeframeText, { color: colors.primary }]}>
            {option.timeframe}
          </Text>
        </View>

        <View style={styles.featuresContainer}>
          {option.features.map((feature, index) => (
            <View key={index} style={styles.featureItem}>
              <Ionicons name="checkmark" size={16} color={colors.success} />
              <Text style={[styles.featureText, { color: colors.textSecondary }]}>
                {feature}
              </Text>
            </View>
          ))}
        </View>
      </Card>
    );
  };

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={[styles.title, { color: colors.text }]}>
            Choose Your Service Type
          </Text>
          <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
            Select the type of tire service you need
          </Text>
        </View>

        {/* Service Options */}
        <View style={styles.optionsContainer}>
          {serviceOptions.map(renderServiceOption)}
        </View>
      </ScrollView>

      {/* Continue Button */}
      <View style={styles.footer}>
        <Button
          title="Continue"
          onPress={handleContinue}
          disabled={!selectedType}
          fullWidth
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: Spacing.lg,
  },
  header: {
    marginBottom: Spacing.xl,
  },
  title: {
    fontSize: Typography.fontSize['2xl'],
    fontWeight: Typography.fontWeight.bold,
    marginBottom: Spacing.sm,
  },
  subtitle: {
    fontSize: Typography.fontSize.base,
    lineHeight: Typography.lineHeight.lg,
  },
  optionsContainer: {
    gap: Spacing.lg,
  },
  serviceCard: {
    borderRadius: BorderRadius.lg,
  },
  serviceHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.base,
  },
  serviceIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.base,
  },
  serviceInfo: {
    flex: 1,
  },
  serviceTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
    marginBottom: Spacing.xs / 2,
  },
  serviceSubtitle: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.medium,
  },
  serviceDescription: {
    fontSize: Typography.fontSize.base,
    lineHeight: Typography.lineHeight.lg,
    marginBottom: Spacing.base,
  },
  timeframeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.base,
    gap: Spacing.sm,
  },
  timeframeText: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.medium,
  },
  featuresContainer: {
    gap: Spacing.sm,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
  },
  featureText: {
    fontSize: Typography.fontSize.sm,
    flex: 1,
  },
  footer: {
    padding: Spacing.lg,
    borderTopWidth: 1,
    borderTopColor: '#E9ECEF',
  },
});
