import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router, useLocalSearchParams } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

import { Colors } from '@/constants/Colors';
import { Typography, Spacing, BorderRadius } from '@/constants/Theme';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Button, OTPInput } from '@/components/ui';
import { useAuth } from '@/contexts';

export default function OTPVerificationScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const { verifyOTP, resendOTP, state, clearError } = useAuth();
  const { phone, email } = useLocalSearchParams<{ phone: string; email: string }>();

  const [otp, setOtp] = useState('');
  const [resendTimer, setResendTimer] = useState(60);
  const [canResend, setCanResend] = useState(false);
  const [otpError, setOtpError] = useState('');

  useEffect(() => {
    if (resendTimer > 0) {
      const timer = setTimeout(() => {
        setResendTimer(resendTimer - 1);
      }, 1000);
      return () => clearTimeout(timer);
    } else {
      setCanResend(true);
    }
  }, [resendTimer]);

  const handleOTPComplete = async (otpValue: string) => {
    setOtp(otpValue);
    setOtpError('');
    
    if (otpValue.length === 6) {
      try {
        await verifyOTP(phone || '', otpValue);
        // Navigation will be handled by the auth state change
      } catch (error) {
        setOtpError('Invalid OTP. Please try again.');
      }
    }
  };

  const handleResendOTP = async () => {
    if (!canResend) return;

    try {
      await resendOTP(phone || '');
      setResendTimer(60);
      setCanResend(false);
      setOtpError('');
      Alert.alert('Success', 'OTP has been resent to your phone number.');
    } catch (error) {
      Alert.alert('Error', 'Failed to resend OTP. Please try again.');
    }
  };

  const handleBackPress = () => {
    router.back();
  };

  const handleChangeNumber = () => {
    router.back();
  };

  const formatPhoneNumber = (phoneNumber: string) => {
    if (!phoneNumber) return '';
    // Simple formatting - you can enhance this based on your needs
    const cleaned = phoneNumber.replace(/\D/g, '');
    if (cleaned.length >= 10) {
      return `${cleaned.slice(0, 3)}****${cleaned.slice(-2)}`;
    }
    return phoneNumber;
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={handleBackPress} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>
          Verification
        </Text>
        <View style={styles.placeholder} />
      </View>

      {/* Content */}
      <View style={styles.content}>
        {/* Icon */}
        <View style={[styles.iconContainer, { backgroundColor: colors.primary + '20' }]}>
          <Ionicons name="shield-checkmark" size={64} color={colors.primary} />
        </View>

        {/* Title and Description */}
        <View style={styles.titleSection}>
          <Text style={[styles.title, { color: colors.text }]}>
            Enter Verification Code
          </Text>
          <Text style={[styles.description, { color: colors.textSecondary }]}>
            We've sent a 6-digit verification code to
          </Text>
          <Text style={[styles.phoneNumber, { color: colors.text }]}>
            {formatPhoneNumber(phone || '')}
          </Text>
        </View>

        {/* Error Message */}
        {(state.error || otpError) && (
          <View style={[styles.errorContainer, { backgroundColor: colors.error + '20' }]}>
            <Ionicons name="alert-circle" size={20} color={colors.error} />
            <Text style={[styles.errorText, { color: colors.error }]}>
              {state.error || otpError}
            </Text>
          </View>
        )}

        {/* OTP Input */}
        <View style={styles.otpSection}>
          <OTPInput
            length={6}
            onComplete={handleOTPComplete}
            error={state.error || otpError}
          />
        </View>

        {/* Resend Section */}
        <View style={styles.resendSection}>
          <Text style={[styles.resendText, { color: colors.textSecondary }]}>
            Didn't receive the code?
          </Text>
          
          {canResend ? (
            <TouchableOpacity onPress={handleResendOTP}>
              <Text style={[styles.resendLink, { color: colors.primary }]}>
                Resend Code
              </Text>
            </TouchableOpacity>
          ) : (
            <Text style={[styles.timerText, { color: colors.textSecondary }]}>
              Resend in {resendTimer}s
            </Text>
          )}
        </View>

        {/* Change Number */}
        <TouchableOpacity onPress={handleChangeNumber} style={styles.changeNumberButton}>
          <Text style={[styles.changeNumberText, { color: colors.primary }]}>
            Change Phone Number
          </Text>
        </TouchableOpacity>
      </View>

      {/* Footer */}
      <View style={styles.footer}>
        <Button
          title="Verify"
          onPress={() => handleOTPComplete(otp)}
          loading={state.isLoading}
          disabled={state.isLoading || otp.length !== 6}
          fullWidth
        />
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.base,
  },
  backButton: {
    padding: Spacing.sm,
  },
  headerTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    paddingHorizontal: Spacing.xl,
    paddingTop: Spacing['2xl'],
    alignItems: 'center',
  },
  iconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Spacing['2xl'],
  },
  titleSection: {
    alignItems: 'center',
    marginBottom: Spacing['2xl'],
  },
  title: {
    fontSize: Typography.fontSize['2xl'],
    fontWeight: Typography.fontWeight.bold,
    marginBottom: Spacing.base,
    textAlign: 'center',
  },
  description: {
    fontSize: Typography.fontSize.base,
    textAlign: 'center',
    marginBottom: Spacing.xs,
  },
  phoneNumber: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Spacing.base,
    borderRadius: BorderRadius.base,
    marginBottom: Spacing.lg,
    width: '100%',
  },
  errorText: {
    fontSize: Typography.fontSize.sm,
    marginLeft: Spacing.sm,
    flex: 1,
  },
  otpSection: {
    width: '100%',
    marginBottom: Spacing['2xl'],
  },
  resendSection: {
    alignItems: 'center',
    marginBottom: Spacing.xl,
  },
  resendText: {
    fontSize: Typography.fontSize.base,
    marginBottom: Spacing.sm,
  },
  resendLink: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.semibold,
  },
  timerText: {
    fontSize: Typography.fontSize.base,
  },
  changeNumberButton: {
    marginBottom: Spacing.xl,
  },
  changeNumberText: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.medium,
  },
  footer: {
    paddingHorizontal: Spacing.xl,
    paddingBottom: Spacing.xl,
  },
});
