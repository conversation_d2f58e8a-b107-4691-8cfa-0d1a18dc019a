import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '@/constants/Colors';
import { Typography, Spacing, BorderRadius, Shadows } from '@/constants/Theme';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Card } from './Card';
import { ServiceQualityBadge, PriceBadge } from './StatusBadge';

interface ServiceProvider {
  id: string;
  name: string;
  rating: number;
  reviewCount: number;
  distance: number;
  estimatedTime: string;
  price: number;
  verified: boolean;
  specialties: string[];
  avatar?: string;
  isAvailable: boolean;
}

interface ServiceProviderCardProps {
  provider: ServiceProvider;
  onPress: (provider: ServiceProvider) => void;
  onFavorite?: (provider: ServiceProvider) => void;
  isFavorite?: boolean;
  style?: any;
}

export const ServiceProviderCard: React.FC<ServiceProviderCardProps> = ({
  provider,
  onPress,
  onFavorite,
  isFavorite = false,
  style,
}) => {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  const handleFavoritePress = () => {
    onFavorite?.(provider);
  };

  return (
    <Card
      variant="elevated"
      padding="lg"
      style={[styles.container, style]}
      onPress={() => onPress(provider)}
    >
      <View style={styles.header}>
        <View style={styles.providerInfo}>
          <View style={styles.avatarContainer}>
            {provider.avatar ? (
              <Image source={{ uri: provider.avatar }} style={styles.avatar} />
            ) : (
              <View style={[styles.avatarPlaceholder, { backgroundColor: colors.primary + '20' }]}>
                <Ionicons name="person" size={24} color={colors.primary} />
              </View>
            )}
            {provider.isAvailable && (
              <View style={[styles.availabilityIndicator, { backgroundColor: colors.success }]} />
            )}
          </View>
          
          <View style={styles.details}>
            <View style={styles.nameRow}>
              <Text style={[styles.providerName, { color: colors.text }]}>
                {provider.name}
              </Text>
              {provider.verified && (
                <Ionicons name="checkmark-circle" size={16} color={colors.success} />
              )}
            </View>
            
            <ServiceQualityBadge 
              rating={provider.rating} 
              verified={provider.verified}
              style={styles.qualityBadge}
            />
            
            <Text style={[styles.reviewCount, { color: colors.textTertiary }]}>
              {provider.reviewCount} reviews
            </Text>
          </View>
        </View>

        <TouchableOpacity onPress={handleFavoritePress} style={styles.favoriteButton}>
          <Ionicons
            name={isFavorite ? "heart" : "heart-outline"}
            size={20}
            color={isFavorite ? colors.error : colors.textTertiary}
          />
        </TouchableOpacity>
      </View>

      <View style={styles.serviceInfo}>
        <View style={styles.locationInfo}>
          <Ionicons name="location-outline" size={16} color={colors.textTertiary} />
          <Text style={[styles.distance, { color: colors.textTertiary }]}>
            {provider.distance.toFixed(1)} km away
          </Text>
          <Text style={[styles.separator, { color: colors.textTertiary }]}>•</Text>
          <Ionicons name="time-outline" size={16} color={colors.textTertiary} />
          <Text style={[styles.estimatedTime, { color: colors.textTertiary }]}>
            {provider.estimatedTime}
          </Text>
        </View>

        <View style={styles.specialties}>
          {provider.specialties.slice(0, 3).map((specialty, index) => (
            <View key={index} style={[styles.specialtyTag, { backgroundColor: colors.primary + '10' }]}>
              <Text style={[styles.specialtyText, { color: colors.primary }]}>
                {specialty}
              </Text>
            </View>
          ))}
          {provider.specialties.length > 3 && (
            <Text style={[styles.moreSpecialties, { color: colors.textTertiary }]}>
              +{provider.specialties.length - 3} more
            </Text>
          )}
        </View>
      </View>

      <View style={styles.footer}>
        <PriceBadge 
          price={provider.price}
          highlighted={provider.rating >= 4.8}
          style={styles.priceBadge}
        />
        
        <View style={[
          styles.availabilityStatus,
          { backgroundColor: provider.isAvailable ? colors.success + '20' : colors.error + '20' }
        ]}>
          <Text style={[
            styles.availabilityText,
            { color: provider.isAvailable ? colors.success : colors.error }
          ]}>
            {provider.isAvailable ? 'Available Now' : 'Busy'}
          </Text>
        </View>
      </View>
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: Spacing.md,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: Spacing.md,
  },
  providerInfo: {
    flexDirection: 'row',
    flex: 1,
  },
  avatarContainer: {
    position: 'relative',
    marginRight: Spacing.md,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  avatarPlaceholder: {
    width: 50,
    height: 50,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
  },
  availabilityIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 12,
    height: 12,
    borderRadius: 6,
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  details: {
    flex: 1,
  },
  nameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.xs,
  },
  providerName: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
    marginRight: Spacing.xs,
  },
  qualityBadge: {
    marginBottom: Spacing.xs,
  },
  reviewCount: {
    fontSize: Typography.fontSize.sm,
  },
  favoriteButton: {
    padding: Spacing.xs,
  },
  serviceInfo: {
    marginBottom: Spacing.md,
  },
  locationInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  distance: {
    fontSize: Typography.fontSize.sm,
    marginLeft: Spacing.xs,
  },
  separator: {
    marginHorizontal: Spacing.sm,
  },
  estimatedTime: {
    fontSize: Typography.fontSize.sm,
    marginLeft: Spacing.xs,
  },
  specialties: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
  },
  specialtyTag: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.sm,
    marginRight: Spacing.xs,
    marginBottom: Spacing.xs,
  },
  specialtyText: {
    fontSize: Typography.fontSize.xs,
    fontWeight: Typography.fontWeight.medium,
  },
  moreSpecialties: {
    fontSize: Typography.fontSize.xs,
    fontStyle: 'italic',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  priceBadge: {
    flex: 1,
    marginRight: Spacing.md,
  },
  availabilityStatus: {
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.full,
  },
  availabilityText: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.semibold,
  },
});
