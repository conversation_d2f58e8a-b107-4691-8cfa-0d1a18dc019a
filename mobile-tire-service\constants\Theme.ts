/**
 * Voltifi App Design System
 * Comprehensive theme configuration including typography, spacing, shadows, and dimensions
 */

import { Dimensions } from "react-native";

const { width: screenWidth, height: screenHeight } = Dimensions.get("window");

// Typography - Professional automotive design system
export const Typography = {
  // Font Families - Modern system fonts with fallbacks
  fontFamily: {
    regular: "SF Pro Display",
    medium: "SF Pro Display",
    semiBold: "SF Pro Display",
    bold: "SF Pro Display",
    mono: "SF Mono",
  },

  // Font Sizes - Optimized for mobile automotive services
  fontSize: {
    xs: 11, // Small labels, badges
    sm: 13, // Secondary text, captions
    base: 15, // Body text, standard content
    md: 16, // Primary body text
    lg: 18, // Subheadings, important text
    xl: 20, // Section headers
    "2xl": 24, // Page titles, card headers
    "3xl": 28, // Screen titles
    "4xl": 32, // Hero text
    "5xl": 40, // Large display text
    "6xl": 48, // Extra large display
  },

  // Line Heights - Optimized for readability
  lineHeight: {
    xs: 16, // Tight for small text
    sm: 18, // Compact for secondary text
    base: 22, // Standard body text
    md: 24, // Primary body text
    lg: 26, // Subheadings
    xl: 28, // Section headers
    "2xl": 32, // Page titles
    "3xl": 36, // Screen titles
    "4xl": 40, // Hero text
    "5xl": 48, // Large display
    "6xl": 56, // Extra large display
  },

  // Font Weights - Professional hierarchy
  fontWeight: {
    light: "300" as const,
    normal: "400" as const,
    medium: "500" as const,
    semibold: "600" as const,
    bold: "700" as const,
    extrabold: "800" as const,
  },

  // Letter Spacing - Refined for automotive branding
  letterSpacing: {
    tighter: -0.5,
    tight: -0.25,
    normal: 0,
    wide: 0.25,
    wider: 0.5,
    widest: 1,
  },
};

// Spacing System (based on 4px grid)
export const Spacing = {
  xs: 4,
  sm: 8,
  md: 12,
  base: 16,
  lg: 20,
  xl: 24,
  "2xl": 32,
  "3xl": 40,
  "4xl": 48,
  "5xl": 64,
  "6xl": 80,
  "7xl": 96,
};

// Border Radius
export const BorderRadius = {
  none: 0,
  sm: 4,
  base: 8,
  md: 12,
  lg: 16,
  xl: 20,
  "2xl": 24,
  "3xl": 32,
  full: 9999,
};

// Shadows - Modern, premium shadow system
export const Shadows = {
  none: {
    shadowColor: "transparent",
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  xs: {
    shadowColor: "#0B1426",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.06,
    shadowRadius: 1,
    elevation: 1,
  },
  sm: {
    shadowColor: "#0B1426",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 3,
    elevation: 3,
  },
  base: {
    shadowColor: "#0B1426",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 6,
  },
  md: {
    shadowColor: "#0B1426",
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.12,
    shadowRadius: 10,
    elevation: 10,
  },
  lg: {
    shadowColor: "#0B1426",
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.15,
    shadowRadius: 15,
    elevation: 15,
  },
  xl: {
    shadowColor: "#0B1426",
    shadowOffset: { width: 0, height: 15 },
    shadowOpacity: 0.18,
    shadowRadius: 20,
    elevation: 20,
  },
  "2xl": {
    shadowColor: "#0B1426",
    shadowOffset: { width: 0, height: 20 },
    shadowOpacity: 0.22,
    shadowRadius: 25,
    elevation: 25,
  },
  // Special automotive shadows
  card: {
    shadowColor: "#0B1426",
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 8,
  },
  button: {
    shadowColor: "#1E3A8A",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 4,
  },
  modal: {
    shadowColor: "#0B1426",
    shadowOffset: { width: 0, height: 25 },
    shadowOpacity: 0.25,
    shadowRadius: 35,
    elevation: 35,
  },
};

// Layout Dimensions
export const Layout = {
  window: {
    width: screenWidth,
    height: screenHeight,
  },
  isSmallDevice: screenWidth < 375,
  headerHeight: 60,
  tabBarHeight: 80,
  buttonHeight: 48,
  inputHeight: 48,
  cardMinHeight: 120,
};

// Animation Durations
export const Animation = {
  fast: 150,
  normal: 300,
  slow: 500,
};

// Z-Index Values
export const ZIndex = {
  hide: -1,
  base: 0,
  dropdown: 1000,
  sticky: 1020,
  fixed: 1030,
  modal: 1040,
  popover: 1050,
  tooltip: 1060,
  toast: 1070,
};

// Icon Sizes
export const IconSizes = {
  xs: 12,
  sm: 16,
  base: 20,
  lg: 24,
  xl: 32,
  "2xl": 40,
  "3xl": 48,
};

// Common Component Styles - Professional automotive design
export const ComponentStyles = {
  // Button variants
  button: {
    height: Layout.buttonHeight,
    borderRadius: BorderRadius.md,
    paddingHorizontal: Spacing.lg,
    ...Shadows.button,
  },
  buttonLarge: {
    height: 56,
    borderRadius: BorderRadius.lg,
    paddingHorizontal: Spacing.xl,
    ...Shadows.button,
  },
  buttonSmall: {
    height: 40,
    borderRadius: BorderRadius.base,
    paddingHorizontal: Spacing.md,
    ...Shadows.sm,
  },

  // Input variants
  input: {
    height: Layout.inputHeight,
    borderRadius: BorderRadius.md,
    paddingHorizontal: Spacing.base,
    borderWidth: 1,
    fontSize: Typography.fontSize.md,
  },
  inputLarge: {
    height: 56,
    borderRadius: BorderRadius.lg,
    paddingHorizontal: Spacing.lg,
    borderWidth: 1,
    fontSize: Typography.fontSize.lg,
  },

  // Card variants
  card: {
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    minHeight: Layout.cardMinHeight,
    ...Shadows.card,
  },
  cardElevated: {
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    minHeight: Layout.cardMinHeight,
    ...Shadows.md,
  },
  cardCompact: {
    borderRadius: BorderRadius.md,
    padding: Spacing.md,
    ...Shadows.sm,
  },

  // Layout containers
  container: {
    flex: 1,
    paddingHorizontal: Spacing.base,
  },
  containerPadded: {
    flex: 1,
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.base,
  },
  section: {
    marginBottom: Spacing.xl,
  },
  sectionCompact: {
    marginBottom: Spacing.lg,
  },

  // Automotive specific components
  serviceCard: {
    borderRadius: BorderRadius.xl,
    padding: Spacing.lg,
    minHeight: 120,
    ...Shadows.card,
  },
  statusBadge: {
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.full,
  },
  priceTag: {
    paddingHorizontal: Spacing.base,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.md,
    ...Shadows.sm,
  },
};

// Breakpoints for responsive design
export const Breakpoints = {
  sm: 576,
  md: 768,
  lg: 992,
  xl: 1200,
};

export default {
  Typography,
  Spacing,
  BorderRadius,
  Shadows,
  Layout,
  Animation,
  ZIndex,
  IconSizes,
  ComponentStyles,
  Breakpoints,
};
