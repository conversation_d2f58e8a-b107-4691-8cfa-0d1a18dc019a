import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

import { Colors } from '@/constants/Colors';
import { Typography, Spacing, BorderRadius, Shadows } from '@/constants/Theme';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Card, Button } from '@/components/ui';
import { useAuth, useApp } from '@/contexts';

interface ProfileMenuItem {
  id: string;
  title: string;
  subtitle?: string;
  icon: string;
  onPress: () => void;
  showChevron?: boolean;
  color?: string;
}

export default function ProfileScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const { state: authState, logout } = useAuth();
  const { state: appState } = useApp();

  const user = authState.user;

  const handleEditProfile = () => {
    Alert.alert('Edit Profile', 'Profile editing functionality will be implemented soon.');
  };

  const handleVehicles = () => {
    Alert.alert('My Vehicles', 'Vehicle management functionality will be implemented soon.');
  };

  const handlePaymentMethods = () => {
    Alert.alert('Payment Methods', 'Payment methods functionality will be implemented soon.');
  };

  const handleNotifications = () => {
    Alert.alert('Notifications', 'Notification settings functionality will be implemented soon.');
  };

  const handleHelp = () => {
    Alert.alert('Help & Support', 'Help & support functionality will be implemented soon.');
  };

  const handleAbout = () => {
    Alert.alert('About', 'Voltifi v1.0.0\nMobile tire service and EV charging platform.');
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Logout', 
          style: 'destructive',
          onPress: () => logout()
        },
      ]
    );
  };

  const accountMenuItems: ProfileMenuItem[] = [
    {
      id: 'edit-profile',
      title: 'Edit Profile',
      subtitle: 'Update your personal information',
      icon: 'person-outline',
      onPress: handleEditProfile,
    },
    {
      id: 'vehicles',
      title: 'My Vehicles',
      subtitle: `${appState.userVehicles.length} vehicle${appState.userVehicles.length !== 1 ? 's' : ''}`,
      icon: 'car-outline',
      onPress: handleVehicles,
    },
    {
      id: 'payment',
      title: 'Payment Methods',
      subtitle: 'Manage your payment options',
      icon: 'card-outline',
      onPress: handlePaymentMethods,
    },
  ];

  const settingsMenuItems: ProfileMenuItem[] = [
    {
      id: 'notifications',
      title: 'Notifications',
      subtitle: 'Manage notification preferences',
      icon: 'notifications-outline',
      onPress: handleNotifications,
    },
    {
      id: 'help',
      title: 'Help & Support',
      subtitle: 'Get help and contact support',
      icon: 'help-circle-outline',
      onPress: handleHelp,
    },
    {
      id: 'about',
      title: 'About',
      subtitle: 'App version and information',
      icon: 'information-circle-outline',
      onPress: handleAbout,
    },
  ];

  const renderMenuItem = (item: ProfileMenuItem) => (
    <TouchableOpacity
      key={item.id}
      style={styles.menuItem}
      onPress={item.onPress}
    >
      <View style={[styles.menuIcon, { backgroundColor: colors.primary + '20' }]}>
        <Ionicons name={item.icon as any} size={24} color={item.color || colors.primary} />
      </View>
      
      <View style={styles.menuContent}>
        <Text style={[styles.menuTitle, { color: colors.text }]}>
          {item.title}
        </Text>
        {item.subtitle && (
          <Text style={[styles.menuSubtitle, { color: colors.textSecondary }]}>
            {item.subtitle}
          </Text>
        )}
      </View>
      
      {item.showChevron !== false && (
        <Ionicons name="chevron-forward" size={20} color={colors.textSecondary} />
      )}
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={[styles.headerTitle, { color: colors.text }]}>
            Profile
          </Text>
        </View>

        {/* User Info Card */}
        <Card
          variant="elevated"
          padding="lg"
          style={styles.userCard}
        >
          <View style={styles.userInfo}>
            <View style={[styles.userAvatar, { backgroundColor: colors.primary }]}>
              <Text style={[styles.userInitials, { color: colors.textLight }]}>
                {user?.firstName?.[0]}{user?.lastName?.[0]}
              </Text>
            </View>
            
            <View style={styles.userDetails}>
              <Text style={[styles.userName, { color: colors.text }]}>
                {user?.firstName} {user?.lastName}
              </Text>
              <Text style={[styles.userEmail, { color: colors.textSecondary }]}>
                {user?.email}
              </Text>
              <Text style={[styles.userPhone, { color: colors.textSecondary }]}>
                {user?.phone}
              </Text>
              
              <View style={styles.userBadges}>
                <View style={[styles.userTypeBadge, { backgroundColor: colors.secondary + '20' }]}>
                  <Text style={[styles.userTypeText, { color: colors.secondary }]}>
                    {user?.userType === 'customer' ? 'Customer' : 'Service Provider'}
                  </Text>
                </View>
                
                {user?.isVerified && (
                  <View style={styles.verifiedBadge}>
                    <Ionicons name="checkmark-circle" size={16} color={colors.success} />
                    <Text style={[styles.verifiedText, { color: colors.success }]}>
                      Verified
                    </Text>
                  </View>
                )}
              </View>
            </View>
          </View>
        </Card>

        {/* Account Section */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Account
          </Text>
          <Card variant="outlined" padding="none" style={styles.menuCard}>
            {accountMenuItems.map((item, index) => (
              <View key={item.id}>
                {renderMenuItem(item)}
                {index < accountMenuItems.length - 1 && (
                  <View style={[styles.separator, { backgroundColor: colors.border }]} />
                )}
              </View>
            ))}
          </Card>
        </View>

        {/* Settings Section */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Settings
          </Text>
          <Card variant="outlined" padding="none" style={styles.menuCard}>
            {settingsMenuItems.map((item, index) => (
              <View key={item.id}>
                {renderMenuItem(item)}
                {index < settingsMenuItems.length - 1 && (
                  <View style={[styles.separator, { backgroundColor: colors.border }]} />
                )}
              </View>
            ))}
          </Card>
        </View>

        {/* Logout Button */}
        <Button
          title="Logout"
          onPress={handleLogout}
          variant="outline"
          style={[styles.logoutButton, { borderColor: colors.error }]}
          textStyle={{ color: colors.error }}
          icon={<Ionicons name="log-out-outline" size={20} color={colors.error} />}
        />

        {/* App Version */}
        <Text style={[styles.versionText, { color: colors.textSecondary }]}>
          Voltifi v1.0.0
        </Text>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: Spacing['2xl'],
  },
  header: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.base,
  },
  headerTitle: {
    fontSize: Typography.fontSize['2xl'],
    fontWeight: Typography.fontWeight.bold,
  },
  userCard: {
    marginHorizontal: Spacing.lg,
    marginBottom: Spacing.xl,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  userAvatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.base,
  },
  userInitials: {
    fontSize: Typography.fontSize['2xl'],
    fontWeight: Typography.fontWeight.bold,
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.semibold,
    marginBottom: Spacing.xs / 2,
  },
  userEmail: {
    fontSize: Typography.fontSize.sm,
    marginBottom: Spacing.xs / 2,
  },
  userPhone: {
    fontSize: Typography.fontSize.sm,
    marginBottom: Spacing.sm,
  },
  userBadges: {
    flexDirection: 'row',
    gap: Spacing.sm,
  },
  userTypeBadge: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs / 2,
    borderRadius: BorderRadius.sm,
  },
  userTypeText: {
    fontSize: Typography.fontSize.xs,
    fontWeight: Typography.fontWeight.medium,
  },
  verifiedBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.xs / 2,
  },
  verifiedText: {
    fontSize: Typography.fontSize.xs,
    fontWeight: Typography.fontWeight.medium,
  },
  section: {
    marginBottom: Spacing.xl,
    paddingHorizontal: Spacing.lg,
  },
  sectionTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
    marginBottom: Spacing.base,
  },
  menuCard: {
    borderRadius: BorderRadius.lg,
    overflow: 'hidden',
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Spacing.base,
  },
  menuIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.base,
  },
  menuContent: {
    flex: 1,
  },
  menuTitle: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.medium,
    marginBottom: Spacing.xs / 2,
  },
  menuSubtitle: {
    fontSize: Typography.fontSize.sm,
  },
  separator: {
    height: 1,
    marginLeft: 64, // Align with content after icon
  },
  logoutButton: {
    marginHorizontal: Spacing.lg,
    marginBottom: Spacing.lg,
  },
  versionText: {
    fontSize: Typography.fontSize.xs,
    textAlign: 'center',
    marginBottom: Spacing.base,
  },
});
