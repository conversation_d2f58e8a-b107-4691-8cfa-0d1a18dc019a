import React from 'react';
import { AuthProvider } from './AuthContext';
import { AppProvider } from './AppContext';

// Combined Providers Component
export const AppProviders: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <AuthProvider>
      <AppProvider>
        {children}
      </AppProvider>
    </AuthProvider>
  );
};

// Export individual providers and hooks
export { AuthProvider, useAuth } from './AuthContext';
export { AppProvider, useApp } from './AppContext';
