import AsyncStorage from '@react-native-async-storage/async-storage';

interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  notifications: {
    bookingUpdates: boolean;
    promotions: boolean;
    emergencyAlerts: boolean;
    pushNotifications: boolean;
  };
  location: {
    allowLocationAccess: boolean;
    defaultLocationId?: string;
  };
  booking: {
    preferredServiceType: 'emergency' | 'scheduled' | null;
    preferredTireCategory: 'premium' | 'mid-range' | 'budget' | null;
    autoSaveLocations: boolean;
  };
  app: {
    onboardingCompleted: boolean;
    lastAppVersion: string;
    language: string;
  };
}

const DEFAULT_PREFERENCES: UserPreferences = {
  theme: 'system',
  notifications: {
    bookingUpdates: true,
    promotions: false,
    emergencyAlerts: true,
    pushNotifications: true,
  },
  location: {
    allowLocationAccess: false,
  },
  booking: {
    preferredServiceType: null,
    preferredTireCategory: null,
    autoSaveLocations: true,
  },
  app: {
    onboardingCompleted: false,
    lastAppVersion: '1.0.0',
    language: 'en',
  },
};

class PreferencesService {
  private static readonly PREFERENCES_KEY = '@voltifi_preferences';
  private static readonly USER_SETTINGS_KEY = '@voltifi_user_settings';

  // Get user preferences
  async getPreferences(): Promise<UserPreferences> {
    try {
      const preferencesJson = await AsyncStorage.getItem(PreferencesService.PREFERENCES_KEY);
      if (preferencesJson) {
        const preferences = JSON.parse(preferencesJson);
        // Merge with defaults to ensure all properties exist
        return { ...DEFAULT_PREFERENCES, ...preferences };
      }
      return DEFAULT_PREFERENCES;
    } catch (error) {
      console.error('Error getting preferences:', error);
      return DEFAULT_PREFERENCES;
    }
  }

  // Save user preferences
  async savePreferences(preferences: Partial<UserPreferences>): Promise<void> {
    try {
      const currentPreferences = await this.getPreferences();
      const updatedPreferences = { ...currentPreferences, ...preferences };
      await AsyncStorage.setItem(
        PreferencesService.PREFERENCES_KEY,
        JSON.stringify(updatedPreferences)
      );
    } catch (error) {
      console.error('Error saving preferences:', error);
      throw error;
    }
  }

  // Update specific preference sections
  async updateTheme(theme: 'light' | 'dark' | 'system'): Promise<void> {
    await this.savePreferences({ theme });
  }

  async updateNotificationSettings(notifications: Partial<UserPreferences['notifications']>): Promise<void> {
    const currentPreferences = await this.getPreferences();
    await this.savePreferences({
      notifications: { ...currentPreferences.notifications, ...notifications }
    });
  }

  async updateLocationSettings(location: Partial<UserPreferences['location']>): Promise<void> {
    const currentPreferences = await this.getPreferences();
    await this.savePreferences({
      location: { ...currentPreferences.location, ...location }
    });
  }

  async updateBookingSettings(booking: Partial<UserPreferences['booking']>): Promise<void> {
    const currentPreferences = await this.getPreferences();
    await this.savePreferences({
      booking: { ...currentPreferences.booking, ...booking }
    });
  }

  async updateAppSettings(app: Partial<UserPreferences['app']>): Promise<void> {
    const currentPreferences = await this.getPreferences();
    await this.savePreferences({
      app: { ...currentPreferences.app, ...app }
    });
  }

  // Specific preference getters
  async getTheme(): Promise<'light' | 'dark' | 'system'> {
    const preferences = await this.getPreferences();
    return preferences.theme;
  }

  async isOnboardingCompleted(): Promise<boolean> {
    const preferences = await this.getPreferences();
    return preferences.app.onboardingCompleted;
  }

  async markOnboardingCompleted(): Promise<void> {
    await this.updateAppSettings({ onboardingCompleted: true });
  }

  async getNotificationSettings(): Promise<UserPreferences['notifications']> {
    const preferences = await this.getPreferences();
    return preferences.notifications;
  }

  async getLocationSettings(): Promise<UserPreferences['location']> {
    const preferences = await this.getPreferences();
    return preferences.location;
  }

  async getBookingSettings(): Promise<UserPreferences['booking']> {
    const preferences = await this.getPreferences();
    return preferences.booking;
  }

  // User-specific settings (separate from preferences)
  async saveUserSetting(key: string, value: any): Promise<void> {
    try {
      const settingsJson = await AsyncStorage.getItem(PreferencesService.USER_SETTINGS_KEY);
      const settings = settingsJson ? JSON.parse(settingsJson) : {};
      settings[key] = value;
      await AsyncStorage.setItem(
        PreferencesService.USER_SETTINGS_KEY,
        JSON.stringify(settings)
      );
    } catch (error) {
      console.error('Error saving user setting:', error);
      throw error;
    }
  }

  async getUserSetting(key: string, defaultValue: any = null): Promise<any> {
    try {
      const settingsJson = await AsyncStorage.getItem(PreferencesService.USER_SETTINGS_KEY);
      if (settingsJson) {
        const settings = JSON.parse(settingsJson);
        return settings[key] !== undefined ? settings[key] : defaultValue;
      }
      return defaultValue;
    } catch (error) {
      console.error('Error getting user setting:', error);
      return defaultValue;
    }
  }

  async removeUserSetting(key: string): Promise<void> {
    try {
      const settingsJson = await AsyncStorage.getItem(PreferencesService.USER_SETTINGS_KEY);
      if (settingsJson) {
        const settings = JSON.parse(settingsJson);
        delete settings[key];
        await AsyncStorage.setItem(
          PreferencesService.USER_SETTINGS_KEY,
          JSON.stringify(settings)
        );
      }
    } catch (error) {
      console.error('Error removing user setting:', error);
      throw error;
    }
  }

  // Cache management
  async clearCache(): Promise<void> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => 
        key.startsWith('@voltifi_cache_') || 
        key.startsWith('@voltifi_temp_')
      );
      if (cacheKeys.length > 0) {
        await AsyncStorage.multiRemove(cacheKeys);
      }
    } catch (error) {
      console.error('Error clearing cache:', error);
      throw error;
    }
  }

  // Reset all preferences to defaults
  async resetPreferences(): Promise<void> {
    try {
      await AsyncStorage.removeItem(PreferencesService.PREFERENCES_KEY);
    } catch (error) {
      console.error('Error resetting preferences:', error);
      throw error;
    }
  }

  // Export/Import preferences (for backup/restore)
  async exportPreferences(): Promise<string> {
    try {
      const preferences = await this.getPreferences();
      return JSON.stringify(preferences, null, 2);
    } catch (error) {
      console.error('Error exporting preferences:', error);
      throw error;
    }
  }

  async importPreferences(preferencesJson: string): Promise<void> {
    try {
      const preferences = JSON.parse(preferencesJson);
      // Validate the structure before saving
      const validatedPreferences = { ...DEFAULT_PREFERENCES, ...preferences };
      await AsyncStorage.setItem(
        PreferencesService.PREFERENCES_KEY,
        JSON.stringify(validatedPreferences)
      );
    } catch (error) {
      console.error('Error importing preferences:', error);
      throw error;
    }
  }

  // App state management
  async saveAppState(state: any): Promise<void> {
    try {
      await AsyncStorage.setItem('@voltifi_app_state', JSON.stringify(state));
    } catch (error) {
      console.error('Error saving app state:', error);
      throw error;
    }
  }

  async getAppState(): Promise<any> {
    try {
      const stateJson = await AsyncStorage.getItem('@voltifi_app_state');
      return stateJson ? JSON.parse(stateJson) : null;
    } catch (error) {
      console.error('Error getting app state:', error);
      return null;
    }
  }

  async clearAppState(): Promise<void> {
    try {
      await AsyncStorage.removeItem('@voltifi_app_state');
    } catch (error) {
      console.error('Error clearing app state:', error);
      throw error;
    }
  }

  // Development/Debug utilities
  async getAllStoredData(): Promise<{ [key: string]: any }> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const voltifiKeys = keys.filter(key => key.startsWith('@voltifi_'));
      const data: { [key: string]: any } = {};
      
      for (const key of voltifiKeys) {
        const value = await AsyncStorage.getItem(key);
        try {
          data[key] = value ? JSON.parse(value) : value;
        } catch {
          data[key] = value; // Store as string if not JSON
        }
      }
      
      return data;
    } catch (error) {
      console.error('Error getting all stored data:', error);
      return {};
    }
  }

  async clearAllData(): Promise<void> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const voltifiKeys = keys.filter(key => key.startsWith('@voltifi_'));
      if (voltifiKeys.length > 0) {
        await AsyncStorage.multiRemove(voltifiKeys);
      }
    } catch (error) {
      console.error('Error clearing all data:', error);
      throw error;
    }
  }
}

export const preferencesService = new PreferencesService();
export type { UserPreferences };
