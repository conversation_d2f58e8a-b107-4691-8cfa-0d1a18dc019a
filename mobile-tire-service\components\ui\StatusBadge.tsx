import React from 'react';
import { View, Text, StyleSheet, ViewStyle, TextStyle } from 'react-native';
import { Colors } from '@/constants/Colors';
import { Typography, Spacing, BorderRadius, Shadows } from '@/constants/Theme';
import { useColorScheme } from '@/hooks/useColorScheme';

interface StatusBadgeProps {
  status: 'pending' | 'confirmed' | 'in-progress' | 'completed' | 'cancelled';
  size?: 'sm' | 'md' | 'lg';
  style?: ViewStyle;
}

export const StatusBadge: React.FC<StatusBadgeProps> = ({
  status,
  size = 'md',
  style,
}) => {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  const getStatusConfig = () => {
    switch (status) {
      case 'pending':
        return {
          backgroundColor: colors.warning + '20',
          textColor: colors.warning,
          text: 'Pending',
          icon: '⏳',
        };
      case 'confirmed':
        return {
          backgroundColor: colors.info + '20',
          textColor: colors.info,
          text: 'Confirmed',
          icon: '✓',
        };
      case 'in-progress':
        return {
          backgroundColor: colors.primary + '20',
          textColor: colors.primary,
          text: 'In Progress',
          icon: '🔧',
        };
      case 'completed':
        return {
          backgroundColor: colors.success + '20',
          textColor: colors.success,
          text: 'Completed',
          icon: '✅',
        };
      case 'cancelled':
        return {
          backgroundColor: colors.error + '20',
          textColor: colors.error,
          text: 'Cancelled',
          icon: '❌',
        };
      default:
        return {
          backgroundColor: colors.border,
          textColor: colors.textSecondary,
          text: 'Unknown',
          icon: '?',
        };
    }
  };

  const getSizeStyles = () => {
    switch (size) {
      case 'sm':
        return {
          paddingHorizontal: Spacing.sm,
          paddingVertical: Spacing.xs,
          fontSize: Typography.fontSize.xs,
          lineHeight: Typography.lineHeight.xs,
        };
      case 'md':
        return {
          paddingHorizontal: Spacing.md,
          paddingVertical: Spacing.sm,
          fontSize: Typography.fontSize.sm,
          lineHeight: Typography.lineHeight.sm,
        };
      case 'lg':
        return {
          paddingHorizontal: Spacing.lg,
          paddingVertical: Spacing.md,
          fontSize: Typography.fontSize.base,
          lineHeight: Typography.lineHeight.base,
        };
    }
  };

  const statusConfig = getStatusConfig();
  const sizeStyles = getSizeStyles();

  const badgeStyle: ViewStyle = {
    backgroundColor: statusConfig.backgroundColor,
    borderRadius: BorderRadius.full,
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-start',
    ...Shadows.xs,
    paddingHorizontal: sizeStyles.paddingHorizontal,
    paddingVertical: sizeStyles.paddingVertical,
  };

  const textStyle: TextStyle = {
    color: statusConfig.textColor,
    fontSize: sizeStyles.fontSize,
    lineHeight: sizeStyles.lineHeight,
    fontWeight: Typography.fontWeight.semibold,
    marginLeft: Spacing.xs,
  };

  return (
    <View style={[badgeStyle, style]}>
      <Text style={{ fontSize: sizeStyles.fontSize }}>
        {statusConfig.icon}
      </Text>
      <Text style={textStyle}>
        {statusConfig.text}
      </Text>
    </View>
  );
};

// Service Quality Badge Component
interface ServiceQualityBadgeProps {
  rating: number;
  verified?: boolean;
  style?: ViewStyle;
}

export const ServiceQualityBadge: React.FC<ServiceQualityBadgeProps> = ({
  rating,
  verified = false,
  style,
}) => {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  const getQualityLevel = (rating: number) => {
    if (rating >= 4.8) return { text: 'Premium', color: colors.premium };
    if (rating >= 4.5) return { text: 'Excellent', color: colors.success };
    if (rating >= 4.0) return { text: 'Good', color: colors.info };
    if (rating >= 3.5) return { text: 'Fair', color: colors.warning };
    return { text: 'Basic', color: colors.error };
  };

  const quality = getQualityLevel(rating);

  return (
    <View style={[styles.qualityBadge, { backgroundColor: quality.color + '15' }, style]}>
      <Text style={[styles.qualityText, { color: quality.color }]}>
        ⭐ {rating.toFixed(1)}
      </Text>
      {verified && (
        <View style={[styles.verifiedIcon, { backgroundColor: colors.success }]}>
          <Text style={styles.verifiedText}>✓</Text>
        </View>
      )}
    </View>
  );
};

// Price Badge Component
interface PriceBadgeProps {
  price: number;
  currency?: string;
  label?: string;
  highlighted?: boolean;
  style?: ViewStyle;
}

export const PriceBadge: React.FC<PriceBadgeProps> = ({
  price,
  currency = '£',
  label = 'From',
  highlighted = false,
  style,
}) => {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  const badgeStyle: ViewStyle = {
    backgroundColor: highlighted ? colors.primary : colors.cardElevated,
    borderRadius: BorderRadius.md,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    ...Shadows.sm,
  };

  return (
    <View style={[badgeStyle, style]}>
      <Text style={[
        styles.priceLabel,
        { color: highlighted ? colors.textLight : colors.textTertiary }
      ]}>
        {label}
      </Text>
      <Text style={[
        styles.priceAmount,
        { color: highlighted ? colors.textLight : colors.text }
      ]}>
        {currency}{price.toFixed(2)}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  qualityBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.full,
    ...Shadows.xs,
  },
  qualityText: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.semibold,
  },
  verifiedIcon: {
    marginLeft: Spacing.xs,
    width: 16,
    height: 16,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  verifiedText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: Typography.fontWeight.bold,
  },
  priceLabel: {
    fontSize: Typography.fontSize.xs,
    fontWeight: Typography.fontWeight.medium,
    marginBottom: 2,
  },
  priceAmount: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.bold,
  },
});
