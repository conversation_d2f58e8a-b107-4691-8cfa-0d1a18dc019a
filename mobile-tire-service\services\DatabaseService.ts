import * as SQLite from 'expo-sqlite';
import { Booking, Vehicle, Location, ServiceProvider, TireBrand, TireService } from '@/types';

class DatabaseService {
  private db: SQLite.SQLiteDatabase | null = null;

  async initialize(): Promise<void> {
    try {
      this.db = await SQLite.openDatabaseAsync('voltifi.db');
      await this.createTables();
      await this.seedInitialData();
    } catch (error) {
      console.error('Database initialization error:', error);
      throw error;
    }
  }

  private async createTables(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    const createTableQueries = [
      // Users table
      `CREATE TABLE IF NOT EXISTS users (
        id TEXT PRIMARY KEY,
        email TEXT UNIQUE NOT NULL,
        phone TEXT UNIQUE NOT NULL,
        firstName TEXT NOT NULL,
        lastName TEXT NOT NULL,
        userType TEXT NOT NULL CHECK (userType IN ('customer', 'service_provider')),
        profileImage TEXT,
        isVerified INTEGER DEFAULT 0,
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL
      )`,

      // Vehicles table
      `CREATE TABLE IF NOT EXISTS vehicles (
        id TEXT PRIMARY KEY,
        registrationNumber TEXT NOT NULL,
        make TEXT NOT NULL,
        model TEXT NOT NULL,
        year INTEGER NOT NULL,
        color TEXT,
        tireSize TEXT NOT NULL,
        loadRating TEXT NOT NULL,
        speedRating TEXT NOT NULL,
        userId TEXT NOT NULL,
        createdAt TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (userId) REFERENCES users (id)
      )`,

      // Locations table
      `CREATE TABLE IF NOT EXISTS locations (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        address TEXT NOT NULL,
        latitude REAL NOT NULL,
        longitude REAL NOT NULL,
        isDefault INTEGER DEFAULT 0,
        userId TEXT NOT NULL,
        createdAt TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (userId) REFERENCES users (id)
      )`,

      // Service providers table
      `CREATE TABLE IF NOT EXISTS service_providers (
        id TEXT PRIMARY KEY,
        businessName TEXT NOT NULL,
        contactPerson TEXT NOT NULL,
        email TEXT NOT NULL,
        phone TEXT NOT NULL,
        address TEXT NOT NULL,
        latitude REAL NOT NULL,
        longitude REAL NOT NULL,
        rating REAL DEFAULT 0,
        reviewCount INTEGER DEFAULT 0,
        isVerified INTEGER DEFAULT 0,
        priceRange TEXT CHECK (priceRange IN ('budget', 'mid-range', 'premium')),
        createdAt TEXT DEFAULT CURRENT_TIMESTAMP
      )`,

      // Tire brands table
      `CREATE TABLE IF NOT EXISTS tire_brands (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        category TEXT NOT NULL CHECK (category IN ('premium', 'mid-range', 'budget')),
        logo TEXT,
        description TEXT,
        createdAt TEXT DEFAULT CURRENT_TIMESTAMP
      )`,

      // Tire services table
      `CREATE TABLE IF NOT EXISTS tire_services (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT NOT NULL,
        basePrice REAL NOT NULL,
        estimatedDuration INTEGER NOT NULL,
        category TEXT NOT NULL CHECK (category IN ('emergency', 'scheduled')),
        createdAt TEXT DEFAULT CURRENT_TIMESTAMP
      )`,

      // Bookings table
      `CREATE TABLE IF NOT EXISTS bookings (
        id TEXT PRIMARY KEY,
        userId TEXT NOT NULL,
        serviceProviderId TEXT NOT NULL,
        vehicleId TEXT NOT NULL,
        locationId TEXT NOT NULL,
        serviceType TEXT NOT NULL CHECK (serviceType IN ('emergency', 'scheduled')),
        scheduledDateTime TEXT,
        status TEXT NOT NULL CHECK (status IN ('pending', 'confirmed', 'in_progress', 'completed', 'cancelled', 'refunded')),
        totalAmount REAL NOT NULL,
        notes TEXT,
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL,
        FOREIGN KEY (userId) REFERENCES users (id),
        FOREIGN KEY (serviceProviderId) REFERENCES service_providers (id),
        FOREIGN KEY (vehicleId) REFERENCES vehicles (id),
        FOREIGN KEY (locationId) REFERENCES locations (id)
      )`,

      // Booking services junction table
      `CREATE TABLE IF NOT EXISTS booking_services (
        id TEXT PRIMARY KEY,
        bookingId TEXT NOT NULL,
        serviceId TEXT NOT NULL,
        tireBrandId TEXT NOT NULL,
        quantity INTEGER NOT NULL,
        unitPrice REAL NOT NULL,
        totalPrice REAL NOT NULL,
        FOREIGN KEY (bookingId) REFERENCES bookings (id),
        FOREIGN KEY (serviceId) REFERENCES tire_services (id),
        FOREIGN KEY (tireBrandId) REFERENCES tire_brands (id)
      )`,

      // Favorite providers table
      `CREATE TABLE IF NOT EXISTS favorite_providers (
        id TEXT PRIMARY KEY,
        userId TEXT NOT NULL,
        serviceProviderId TEXT NOT NULL,
        createdAt TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (userId) REFERENCES users (id),
        FOREIGN KEY (serviceProviderId) REFERENCES service_providers (id),
        UNIQUE(userId, serviceProviderId)
      )`,
    ];

    for (const query of createTableQueries) {
      await this.db.execAsync(query);
    }
  }

  private async seedInitialData(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    // Check if data already exists
    const brandCount = await this.db.getFirstAsync('SELECT COUNT(*) as count FROM tire_brands');
    if ((brandCount as any)?.count > 0) return;

    // Seed tire brands
    const tireBrands = [
      { id: '1', name: 'Michelin', category: 'premium', description: 'Premium French tire manufacturer' },
      { id: '2', name: 'Bridgestone', category: 'premium', description: 'Japanese premium tire brand' },
      { id: '3', name: 'Continental', category: 'mid-range', description: 'German engineering excellence' },
      { id: '4', name: 'Goodyear', category: 'mid-range', description: 'American tire innovation' },
      { id: '5', name: 'Hankook', category: 'budget', description: 'Korean value for money' },
      { id: '6', name: 'Kumho', category: 'budget', description: 'Affordable Korean tires' },
    ];

    for (const brand of tireBrands) {
      await this.db.runAsync(
        'INSERT INTO tire_brands (id, name, category, description) VALUES (?, ?, ?, ?)',
        [brand.id, brand.name, brand.category, brand.description]
      );
    }

    // Seed tire services
    const tireServices = [
      { id: '1', name: 'Tire Replacement', description: 'Complete tire replacement service', basePrice: 80, estimatedDuration: 45, category: 'scheduled' },
      { id: '2', name: 'Emergency Tire Change', description: 'Emergency roadside tire change', basePrice: 120, estimatedDuration: 30, category: 'emergency' },
      { id: '3', name: 'Tire Repair', description: 'Puncture repair service', basePrice: 25, estimatedDuration: 20, category: 'scheduled' },
    ];

    for (const service of tireServices) {
      await this.db.runAsync(
        'INSERT INTO tire_services (id, name, description, basePrice, estimatedDuration, category) VALUES (?, ?, ?, ?, ?, ?)',
        [service.id, service.name, service.description, service.basePrice, service.estimatedDuration, service.category]
      );
    }
  }

  // Tire Brands
  async getTireBrands(): Promise<TireBrand[]> {
    if (!this.db) throw new Error('Database not initialized');
    
    const result = await this.db.getAllAsync('SELECT * FROM tire_brands ORDER BY name');
    return result as TireBrand[];
  }

  // Tire Services
  async getTireServices(): Promise<TireService[]> {
    if (!this.db) throw new Error('Database not initialized');
    
    const result = await this.db.getAllAsync('SELECT * FROM tire_services ORDER BY name');
    return result as TireService[];
  }

  // Vehicles
  async saveVehicle(vehicle: Vehicle): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');
    
    await this.db.runAsync(
      `INSERT OR REPLACE INTO vehicles 
       (id, registrationNumber, make, model, year, color, tireSize, loadRating, speedRating, userId) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        vehicle.id,
        vehicle.registrationNumber,
        vehicle.make,
        vehicle.model,
        vehicle.year,
        vehicle.color || null,
        vehicle.tireSize,
        vehicle.loadRating,
        vehicle.speedRating,
        vehicle.userId,
      ]
    );
  }

  async getUserVehicles(userId: string): Promise<Vehicle[]> {
    if (!this.db) throw new Error('Database not initialized');
    
    const result = await this.db.getAllAsync(
      'SELECT * FROM vehicles WHERE userId = ? ORDER BY createdAt DESC',
      [userId]
    );
    return result as Vehicle[];
  }

  // Locations
  async saveLocation(location: Location): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');
    
    await this.db.runAsync(
      `INSERT OR REPLACE INTO locations 
       (id, name, address, latitude, longitude, isDefault, userId) 
       VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [
        location.id,
        location.name,
        location.address,
        location.latitude,
        location.longitude,
        location.isDefault ? 1 : 0,
        location.userId,
      ]
    );
  }

  async getUserLocations(userId: string): Promise<Location[]> {
    if (!this.db) throw new Error('Database not initialized');
    
    const result = await this.db.getAllAsync(
      'SELECT * FROM locations WHERE userId = ? ORDER BY isDefault DESC, createdAt DESC',
      [userId]
    );
    return result.map(row => ({
      ...(row as any),
      isDefault: Boolean((row as any).isDefault),
    })) as Location[];
  }

  async deleteLocation(locationId: string): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');
    
    await this.db.runAsync('DELETE FROM locations WHERE id = ?', [locationId]);
  }

  // Bookings
  async saveBooking(booking: Booking): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');
    
    await this.db.runAsync(
      `INSERT OR REPLACE INTO bookings 
       (id, userId, serviceProviderId, vehicleId, locationId, serviceType, scheduledDateTime, status, totalAmount, notes, createdAt, updatedAt) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        booking.id,
        booking.userId,
        booking.serviceProviderId,
        booking.vehicleId,
        booking.locationId,
        booking.serviceType,
        booking.scheduledDateTime || null,
        booking.status,
        booking.totalAmount,
        booking.notes || null,
        booking.createdAt,
        booking.updatedAt,
      ]
    );

    // Save booking services
    for (const service of booking.services) {
      await this.db.runAsync(
        `INSERT OR REPLACE INTO booking_services 
         (id, bookingId, serviceId, tireBrandId, quantity, unitPrice, totalPrice) 
         VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [
          `${booking.id}_${service.serviceId}`,
          booking.id,
          service.serviceId,
          service.tireBrandId,
          service.quantity,
          service.unitPrice,
          service.totalPrice,
        ]
      );
    }
  }

  async getUserBookings(userId: string): Promise<Booking[]> {
    if (!this.db) throw new Error('Database not initialized');
    
    const result = await this.db.getAllAsync(
      'SELECT * FROM bookings WHERE userId = ? ORDER BY createdAt DESC',
      [userId]
    );
    return result as Booking[];
  }

  async updateBookingStatus(bookingId: string, status: string): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');
    
    await this.db.runAsync(
      'UPDATE bookings SET status = ?, updatedAt = ? WHERE id = ?',
      [status, new Date().toISOString(), bookingId]
    );
  }

  // Favorite Providers
  async addFavoriteProvider(userId: string, serviceProviderId: string): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');
    
    await this.db.runAsync(
      'INSERT OR IGNORE INTO favorite_providers (id, userId, serviceProviderId) VALUES (?, ?, ?)',
      [`${userId}_${serviceProviderId}`, userId, serviceProviderId]
    );
  }

  async removeFavoriteProvider(userId: string, serviceProviderId: string): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');
    
    await this.db.runAsync(
      'DELETE FROM favorite_providers WHERE userId = ? AND serviceProviderId = ?',
      [userId, serviceProviderId]
    );
  }

  async getFavoriteProviders(userId: string): Promise<string[]> {
    if (!this.db) throw new Error('Database not initialized');
    
    const result = await this.db.getAllAsync(
      'SELECT serviceProviderId FROM favorite_providers WHERE userId = ?',
      [userId]
    );
    return result.map((row: any) => row.serviceProviderId);
  }
}

export const databaseService = new DatabaseService();
