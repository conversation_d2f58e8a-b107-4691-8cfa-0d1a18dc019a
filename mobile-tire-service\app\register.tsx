import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

import { Colors } from '@/constants/Colors';
import { Typography, Spacing, BorderRadius } from '@/constants/Theme';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Button, Input, Card } from '@/components/ui';
import { useAuth } from '@/contexts';
import { UserRegistration } from '@/types';

export default function RegisterScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const { register, state, clearError } = useAuth();

  const [formData, setFormData] = useState<UserRegistration>({
    email: '',
    phone: '',
    firstName: '',
    lastName: '',
    password: '',
    confirmPassword: '',
    userType: 'customer',
    agreeToTerms: false,
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [errors, setErrors] = useState<Partial<UserRegistration>>({});

  const validateForm = (): boolean => {
    const newErrors: Partial<UserRegistration> = {};

    if (!formData.firstName.trim()) {
      newErrors.firstName = 'First name is required';
    }

    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Last name is required';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required';
    } else if (!/^\+?[\d\s-()]+$/.test(formData.phone)) {
      newErrors.phone = 'Please enter a valid phone number';
    }

    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }

    if (!formData.confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your password';
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }

    if (!formData.agreeToTerms) {
      newErrors.agreeToTerms = 'You must agree to the terms and conditions';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleRegister = async () => {
    if (!validateForm()) return;

    try {
      await register(formData);
      // Navigate to OTP verification
      router.push({
        pathname: '/otp-verification',
        params: { phone: formData.phone, email: formData.email },
      });
    } catch (error) {
      // Error is handled by the auth context
    }
  };

  const handleInputChange = (field: keyof UserRegistration, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
    // Clear global error
    if (state.error) {
      clearError();
    }
  };

  const handleBackPress = () => {
    router.back();
  };

  const handleLogin = () => {
    router.push('/login');
  };

  const UserTypeCard = ({ 
    type, 
    title, 
    description, 
    icon 
  }: { 
    type: 'customer' | 'service_provider'; 
    title: string; 
    description: string; 
    icon: string;
  }) => (
    <Card
      onPress={() => handleInputChange('userType', type)}
      variant={formData.userType === type ? 'elevated' : 'outlined'}
      padding="md"
      style={[
        styles.userTypeCard,
        formData.userType === type && { borderColor: colors.primary, borderWidth: 2 }
      ]}
    >
      <View style={styles.userTypeContent}>
        <View style={[
          styles.userTypeIcon,
          { backgroundColor: formData.userType === type ? colors.primary : colors.border }
        ]}>
          <Ionicons 
            name={icon as any} 
            size={24} 
            color={formData.userType === type ? colors.textLight : colors.textSecondary} 
          />
        </View>
        <View style={styles.userTypeText}>
          <Text style={[
            styles.userTypeTitle,
            { color: formData.userType === type ? colors.primary : colors.text }
          ]}>
            {title}
          </Text>
          <Text style={[styles.userTypeDescription, { color: colors.textSecondary }]}>
            {description}
          </Text>
        </View>
        {formData.userType === type && (
          <Ionicons name="checkmark-circle" size={24} color={colors.primary} />
        )}
      </View>
    </Card>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <KeyboardAvoidingView
        style={styles.keyboardAvoid}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Header */}
          <View style={styles.header}>
            <TouchableOpacity onPress={handleBackPress} style={styles.backButton}>
              <Ionicons name="arrow-back" size={24} color={colors.text} />
            </TouchableOpacity>
            <Text style={[styles.headerTitle, { color: colors.text }]}>
              Create Account
            </Text>
            <View style={styles.placeholder} />
          </View>

          {/* Content */}
          <View style={styles.content}>
            <View style={styles.titleSection}>
              <Text style={[styles.title, { color: colors.text }]}>
                Join Voltifi
              </Text>
              <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
                Create your account to get started
              </Text>
            </View>

            {/* Error Message */}
            {state.error && (
              <View style={[styles.errorContainer, { backgroundColor: colors.error + '20' }]}>
                <Ionicons name="alert-circle" size={20} color={colors.error} />
                <Text style={[styles.errorText, { color: colors.error }]}>
                  {state.error}
                </Text>
              </View>
            )}

            {/* User Type Selection */}
            <View style={styles.section}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>
                I am a
              </Text>
              <View style={styles.userTypeContainer}>
                <UserTypeCard
                  type="customer"
                  title="Customer"
                  description="Book tire services"
                  icon="person-outline"
                />
                <UserTypeCard
                  type="service_provider"
                  title="Service Provider"
                  description="Provide tire services"
                  icon="construct-outline"
                />
              </View>
            </View>

            {/* Form */}
            <View style={styles.form}>
              <View style={styles.nameRow}>
                <Input
                  label="First Name"
                  placeholder="Enter first name"
                  value={formData.firstName}
                  onChangeText={(value) => handleInputChange('firstName', value)}
                  error={errors.firstName}
                  containerStyle={styles.nameInput}
                  required
                />
                <Input
                  label="Last Name"
                  placeholder="Enter last name"
                  value={formData.lastName}
                  onChangeText={(value) => handleInputChange('lastName', value)}
                  error={errors.lastName}
                  containerStyle={styles.nameInput}
                  required
                />
              </View>

              <Input
                label="Email Address"
                placeholder="Enter your email"
                value={formData.email}
                onChangeText={(value) => handleInputChange('email', value)}
                error={errors.email}
                keyboardType="email-address"
                autoCapitalize="none"
                leftIcon={
                  <Ionicons name="mail-outline" size={20} color={colors.textSecondary} />
                }
                required
              />

              <Input
                label="Phone Number"
                placeholder="Enter your phone number"
                value={formData.phone}
                onChangeText={(value) => handleInputChange('phone', value)}
                error={errors.phone}
                keyboardType="phone-pad"
                leftIcon={
                  <Ionicons name="call-outline" size={20} color={colors.textSecondary} />
                }
                required
              />

              <Input
                label="Password"
                placeholder="Create a password"
                value={formData.password}
                onChangeText={(value) => handleInputChange('password', value)}
                error={errors.password}
                secureTextEntry={!showPassword}
                leftIcon={
                  <Ionicons name="lock-closed-outline" size={20} color={colors.textSecondary} />
                }
                rightIcon={
                  <Ionicons
                    name={showPassword ? 'eye-off-outline' : 'eye-outline'}
                    size={20}
                    color={colors.textSecondary}
                  />
                }
                onRightIconPress={() => setShowPassword(!showPassword)}
                required
              />

              <Input
                label="Confirm Password"
                placeholder="Confirm your password"
                value={formData.confirmPassword}
                onChangeText={(value) => handleInputChange('confirmPassword', value)}
                error={errors.confirmPassword}
                secureTextEntry={!showConfirmPassword}
                leftIcon={
                  <Ionicons name="lock-closed-outline" size={20} color={colors.textSecondary} />
                }
                rightIcon={
                  <Ionicons
                    name={showConfirmPassword ? 'eye-off-outline' : 'eye-outline'}
                    size={20}
                    color={colors.textSecondary}
                  />
                }
                onRightIconPress={() => setShowConfirmPassword(!showConfirmPassword)}
                required
              />

              {/* Terms and Conditions */}
              <TouchableOpacity
                style={styles.termsContainer}
                onPress={() => handleInputChange('agreeToTerms', !formData.agreeToTerms)}
              >
                <View style={[
                  styles.checkbox,
                  { borderColor: formData.agreeToTerms ? colors.primary : colors.border },
                  formData.agreeToTerms && { backgroundColor: colors.primary }
                ]}>
                  {formData.agreeToTerms && (
                    <Ionicons name="checkmark" size={16} color={colors.textLight} />
                  )}
                </View>
                <Text style={[styles.termsText, { color: colors.textSecondary }]}>
                  I agree to the{' '}
                  <Text style={[styles.linkText, { color: colors.primary }]}>
                    Terms of Service
                  </Text>
                  {' '}and{' '}
                  <Text style={[styles.linkText, { color: colors.primary }]}>
                    Privacy Policy
                  </Text>
                </Text>
              </TouchableOpacity>
              {errors.agreeToTerms && (
                <Text style={[styles.errorText, { color: colors.error }]}>
                  {errors.agreeToTerms}
                </Text>
              )}
            </View>

            {/* Register Button */}
            <Button
              title="Create Account"
              onPress={handleRegister}
              loading={state.isLoading}
              disabled={state.isLoading}
              fullWidth
              style={styles.registerButton}
            />

            {/* Login Link */}
            <View style={styles.loginSection}>
              <Text style={[styles.loginText, { color: colors.textSecondary }]}>
                Already have an account?{' '}
                <Text
                  style={[styles.loginLink, { color: colors.primary }]}
                  onPress={handleLogin}
                >
                  Sign In
                </Text>
              </Text>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardAvoid: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.base,
  },
  backButton: {
    padding: Spacing.sm,
  },
  headerTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    paddingHorizontal: Spacing.xl,
    paddingTop: Spacing.base,
  },
  titleSection: {
    marginBottom: Spacing.xl,
  },
  title: {
    fontSize: Typography.fontSize['3xl'],
    fontWeight: Typography.fontWeight.bold,
    marginBottom: Spacing.sm,
  },
  subtitle: {
    fontSize: Typography.fontSize.base,
    lineHeight: Typography.lineHeight.lg,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Spacing.base,
    borderRadius: BorderRadius.base,
    marginBottom: Spacing.lg,
  },
  errorText: {
    fontSize: Typography.fontSize.sm,
    marginLeft: Spacing.sm,
    flex: 1,
  },
  section: {
    marginBottom: Spacing.xl,
  },
  sectionTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
    marginBottom: Spacing.base,
  },
  userTypeContainer: {
    gap: Spacing.base,
  },
  userTypeCard: {
    borderRadius: BorderRadius.md,
  },
  userTypeContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  userTypeIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.base,
  },
  userTypeText: {
    flex: 1,
  },
  userTypeTitle: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.semibold,
    marginBottom: Spacing.xs / 2,
  },
  userTypeDescription: {
    fontSize: Typography.fontSize.sm,
  },
  form: {
    marginBottom: Spacing.xl,
  },
  nameRow: {
    flexDirection: 'row',
    gap: Spacing.base,
  },
  nameInput: {
    flex: 1,
  },
  termsContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginTop: Spacing.base,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.sm,
    marginTop: 2,
  },
  termsText: {
    fontSize: Typography.fontSize.sm,
    lineHeight: Typography.lineHeight.sm,
    flex: 1,
  },
  linkText: {
    fontWeight: Typography.fontWeight.medium,
  },
  registerButton: {
    marginBottom: Spacing.xl,
  },
  loginSection: {
    alignItems: 'center',
    marginTop: Spacing.lg,
    marginBottom: Spacing.xl,
  },
  loginText: {
    fontSize: Typography.fontSize.base,
  },
  loginLink: {
    fontWeight: Typography.fontWeight.semibold,
  },
});
